name: Update Contributors

on:
  push:
    branches:
      - main
  workflow_dispatch:  # Allows manual triggering

env:
  NODE_VERSION: 20.18.1
  PNPM_VERSION: 10.8.1

jobs:
  update-contributors:
    runs-on: ubuntu-latest
    permissions:
      contents: write  # Needed for pushing changes
      pull-requests: write  # Needed for creating PRs
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      - name: Disable <PERSON><PERSON>
        run: |
          echo "HUSKY=0" >> $GITHUB_ENV
          git config --global core.hooksPath /dev/null
      - name: Install dependencies
        run: pnpm install
      - name: Update contributors and format
        run: |
          pnpm update-contributors
          npx prettier --write README.md
          if git diff --quiet; then echo "changes=false" >> $GITHUB_OUTPUT; else echo "changes=true" >> $GITHUB_OUTPUT; fi
        id: check-changes
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Create Pull Request
        if: steps.check-changes.outputs.changes == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "docs: update contributors list [skip ci]"
          committer: "github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>"
          branch: update-contributors
          delete-branch: true
          title: "Update contributors list"
          body: |
            Automated update of contributors list and related files

            This PR was created automatically by a GitHub Action workflow and includes all changed files.
          base: main
