{"greeting": "W<PERSON>my w Roo Code", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Zobacz więcej", "seeLess": "Zobacz mniej", "tokens": "Tokeny:", "cache": "<PERSON><PERSON><PERSON>ć podręczna:", "apiCost": "Koszt API:", "contextWindow": "Okno kontekstu:", "closeAndStart": "Zamknij zadanie i rozpocznij nowe", "export": "Eksportuj historię zadań", "delete": "<PERSON><PERSON><PERSON> zadanie (Shi<PERSON> + <PERSON><PERSON><PERSON>, aby pomin<PERSON> potwierdzenie)", "condenseContext": "Inteligentnie skondensuj kontekst"}, "unpin": "Odepnij", "pin": "Przypnij", "tokenProgress": {"availableSpace": "Dostę<PERSON><PERSON> miej<PERSON>ce: {{amount}} tokenów", "tokensUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tokeny: {{used}} z {{total}}", "reservedForResponse": "Zarezerwowane dla odpowiedzi modelu: {{amount}} tokenów"}, "retry": {"title": "Ponów", "tooltip": "Spróbuj ponownie wykonać operację"}, "startNewTask": {"title": "Rozpocznij nowe zadanie", "tooltip": "Rozpocznij nowe zadanie"}, "proceedAnyways": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mimo to", "tooltip": "Kontynuuj podczas wykonywania polecenia"}, "save": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Zapisz zmiany w pliku"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tę akcję"}, "completeSubtaskAndReturn": "Zakończ podzadanie i wróć", "approve": {"title": "Zatwierdź", "tooltip": "Zatwierdź tę akcję"}, "runCommand": {"title": "Uru<PERSON><PERSON> polecenie", "tooltip": "<PERSON><PERSON><PERSON><PERSON> to polecenie"}, "proceedWhileRunning": {"title": "Kontynuuj podczas wykonywania", "tooltip": "Kontynuuj pomimo ostrzeżeń"}, "killCommand": {"title": "Zatrzymaj <PERSON>nie", "tooltip": "Zatrzymaj bieżące polecenie"}, "resumeTask": {"title": "Wznów zadanie", "tooltip": "Kontynuuj bieżące zadanie"}, "terminate": {"title": "Zakończ", "tooltip": "Zakończ bieżące zadanie"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Anuluj bieżącą operację"}, "scrollToBottom": "Przewiń do dołu czatu", "about": "Gene<PERSON>j, refaktoryzuj i debuguj kod z pomocą sztucznej inteligencji. Sprawdź naszą <DocsLink>dokumentację</DocsLink>, aby dowiedzieć się więcej.", "onboarding": "<strong>Twoja lista zadań w tym obszarze roboczym jest pusta.</strong> Zacznij od wpisania zadania poniżej. <PERSON>e wiesz, jak zaczą<PERSON>? Przeczytaj więcej o tym, co Roo może dla Ciebie zrobić w <DocsLink>dokumentacji</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Zadania bumerangowe", "description": "Podziel zadania na mniejsze, łatwiejsze do zarządzania części."}, "stickyModels": {"title": "Tryby trwałe", "description": "<PERSON><PERSON><PERSON> tryb zapamiętuje ostatnio używany model"}, "tools": {"title": "Narzędzia", "description": "Pozwól sztucznej inteligencji rozwiązywać problemy, prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sie<PERSON>, uruchamiając polecenia i nie tylko."}, "customizableModes": {"title": "Konfigurow<PERSON>ne tryby", "description": "Wyspecjalizowane persona z własnymi zachowaniami i przypisanymi modelami"}}, "selectMode": "<PERSON><PERSON><PERSON><PERSON> tryb interakcji", "selectApiConfig": "<PERSON><PERSON><PERSON><PERSON> konfigurację <PERSON>", "enhancePrompt": "Ulepsz podpowiedź dodatkowym kontekstem", "addImages": "Dodaj obrazy do wiadomości", "sendMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeMessage": "<PERSON><PERSON><PERSON> wiadom<PERSON>ść...", "typeTask": "Wpisz swoje zadanie tutaj...", "addContext": "@ aby do<PERSON><PERSON> konte<PERSON>t, / aby z<PERSON><PERSON> tryb", "dragFiles": "prz<PERSON><PERSON><PERSON><PERSON> shift, aby przecią<PERSON><PERSON>ć pliki", "dragFilesImages": "prz<PERSON><PERSON><PERSON><PERSON> shift, aby przec<PERSON><PERSON><PERSON><PERSON><PERSON> pliki/obrazy", "enhancePromptDescription": "Przycisk 'Uleps<PERSON> podpowiedź' pomaga ul<PERSON>, dostar<PERSON><PERSON><PERSON><PERSON> dodatkowy kontekst, wyjaśnienia lub przeformułowania. Spróbuj wpisać prośbę tutaj i kliknij przycisk ponownie, aby <PERSON><PERSON><PERSON>, jak to dzia<PERSON>.", "errorReadingFile": "Błąd odczytu pliku:", "noValidImages": "Nie przetworzono żadnych prawidłowych obrazów", "separator": "Separator", "edit": "Edytuj...", "forNextMode": "dla następnego trybu", "error": "Błąd", "diffError": {"title": "<PERSON><PERSON><PERSON><PERSON>a"}, "troubleMessage": "Roo ma problemy...", "apiRequest": {"title": "Zapytanie API", "failed": "Zapytanie API nie powiodło się", "streaming": "Zapytanie API...", "cancelled": "Zapytanie API anulowane", "streamingFailed": "Strumieniowanie API nie powiodło się"}, "checkpoint": {"initial": "Początkowy punkt kontrolny", "regular": "Punkt kontrolny", "initializingWarning": "Trwa inicjalizacja punktu kontrolnego... <PERSON><PERSON><PERSON> to trwa zbyt długo, moż<PERSON>z wyłączyć punkty kontrolne w <settingsLink>ustawieniach</settingsLink> i uruchomić zadanie ponownie.", "menu": {"viewDiff": "Zobacz różnice", "restore": "Przywróć punkt kontrolny", "restoreFiles": "P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pliki", "restoreFilesDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie.", "restoreFilesAndTask": "Przywróć pliki i zadanie", "confirm": "Potwierdź", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON> akcji nie można <PERSON>.", "restoreFilesAndTaskDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie i usuwa wszystkie wiadomości po tym punkcie."}, "current": "Bieżący"}, "instructions": {"wantsToFetch": "Roo chce pobrać szczegółowe instrukcje, aby pomóc w bieżącym zadaniu"}, "fileOperations": {"wantsToRead": "Roo chce przeczytać ten plik:", "wantsToReadOutsideWorkspace": "Roo chce przeczytać ten plik poza obszarem roboczym:", "didRead": "<PERSON><PERSON> przeczytał ten plik:", "wantsToEdit": "<PERSON><PERSON> ch<PERSON> ed<PERSON> ten plik:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON> ch<PERSON> edyt<PERSON>ć ten plik poza obszarem roboczym:", "wantsToCreate": "Roo chce utworzyć nowy plik:", "wantsToSearchReplace": "<PERSON><PERSON> chce wykonać wyszukiwanie i zamianę w tym pliku:", "didSearchReplace": "Roo wykonał wyszukiwanie i zamianę w tym pliku:", "wantsToInsert": "Roo chce w<PERSON>wić zawartość do tego pliku:", "wantsToInsertWithLineNumber": "<PERSON><PERSON> chce w<PERSON><PERSON><PERSON> zawa<PERSON> do tego pliku w linii {{lineNumber}}:", "wantsToInsertAtEnd": "Roo chce dodać zawartość na końcu tego pliku:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo chce zobaczyć pliki najwyższego poziomu w tym katalogu:", "didViewTopLevel": "Roo zobaczył pliki najwyższego poziomu w tym katalogu:", "wantsToViewRecursive": "<PERSON>oo chce rekurencyjnie zobaczyć wszystkie pliki w tym katalogu:", "didViewRecursive": "Roo rekurencyjnie zobaczył wszystkie pliki w tym katalogu:", "wantsToViewDefinitions": "<PERSON><PERSON> chce zobaczyć nazwy definicji kodu źródłowego używane w tym katalogu:", "didViewDefinitions": "<PERSON><PERSON> zobaczył nazwy definicji kodu źródłowego używane w tym katalogu:", "wantsToSearch": "<PERSON><PERSON> chce przeszukać ten katalog w poszukiwaniu <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> przeszukał ten katalog w poszukiwaniu <code>{{regex}}</code>:"}, "commandOutput": "Wyjście polecenia", "response": "<PERSON><PERSON><PERSON><PERSON><PERSON>ź", "arguments": "Argumenty", "mcp": {"wantsToUseTool": "Roo chce użyć narzędzia na serwerze MCP {{serverName}}:", "wantsToAccessResource": "Roo chce uzyskać dostęp do zasobu na serwerze MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON>oo chce przełączyć się na tryb <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON>oo chce przełączyć się na tryb <code>{{mode}}</code> ponieważ: {{reason}}", "didSwitch": "<PERSON><PERSON> przełączył się na tryb <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON> przełączył się na tryb <code>{{mode}}</code> ponieważ: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON> chce utworzyć nowe podzadanie w trybie <code>{{mode}}</code>:", "wantsToFinish": "Roo chce zakończyć to podzadanie", "newTaskContent": "Instrukcje podzadania", "completionContent": "Podzadanie zakończone", "resultContent": "Wyniki podzadania", "defaultResult": "Proszę kontynuować następne zadanie.", "completionInstructions": "Podzadanie zakończone! Możesz przejrzeć wyniki i zasugerować poprawki lub następne kroki. Jeśli wszystko wygląda dobrze, pot<PERSON><PERSON><PERSON>, aby zwrócić wynik do zadania nadrzędnego."}, "questions": {"hasQuestion": "Roo ma pytanie:"}, "taskCompleted": "Zadanie zakończone", "powershell": {"issues": "Wygląda na to, że masz problemy z Windows PowerShell, proszę zapoznaj się z tym"}, "autoApprove": {"title": "Automatyczne zatwierdzanie:", "none": "Brak", "description": "Automatyczne zatwierdzanie pozwala Roo Code wykonywać działania bez pytania o pozwolenie. Włącz tylko dla działań, którym w pełni ufasz. Bardziej szczegółowa konfiguracja dostępna w <settingsLink>Ustawieniach</settingsLink>."}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}} s"}, "contextCondense": {"title": "Kontekst skondensowany", "condensing": "Kondensowanie kontekstu...", "tokens": "tokeny"}, "followUpSuggest": {"copyToInput": "Kopiuj do pola wprowadzania (lub Shift + kliknięcie)"}, "announcement": {"title": "🎉 Roo Code {{version}} wydany", "description": "Roo Code {{version}} przynosi potężne nowe funkcje i ulepszenia na podstawie Twoich opinii.", "whatsNew": "Co nowego", "feature1": "<bold>Modele podglądu Gemini 2.5 Flash</bold>: Uzyskaj dostęp do najnowszych modeli Gemini Flash zapewniających szybsze i bardziej efektywne odpowiedzi", "feature2": "<bold>Inteligentne kondensowanie kontekstu</bold>: Nowy przycisk w nagłówku zadania pozwala na inteligentne kondensowanie treści z wizualną informacją zwrotną", "feature3": "<bold>Wsparcie YAML dla definicji trybów</bold>: Twórz i dostosowuj tryby łatwiej dzięki wsparciu dla YAML", "hideButton": "<PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Uzyskaj więcej szczegółów i dołącz do dyskusji na <discordLink>Discord</discordLink> i <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Roo chce użyć przeglądarki:", "consoleLogs": "<PERSON><PERSON> k<PERSON>", "noNewLogs": "(Brak nowych logów)", "screenshot": "Zrzut ekranu przeglądarki", "cursor": "kursor", "navigation": {"step": "<PERSON>rok {{current}} z {{total}}", "previous": "Poprzedni", "next": "Następny"}, "sessionStarted": "Sesja przeglądarki rozpoczęta", "actions": {"title": "Akcja przeglądarki: ", "launch": "Uruchom przeglądarkę na {{url}}", "click": "<PERSON><PERSON><PERSON>j ({{coordinate}})", "type": "Wpisz \"{{text}}\"", "scrollDown": "Przewiń w dół", "scrollUp": "Przewiń w górę", "close": "Zamknij przeglądarkę"}}, "codeblock": {"tooltips": {"expand": "Rozwiń blok kodu", "collapse": "Zwiń blok kodu", "enable_wrap": "Włącz zawijanie wierszy", "disable_wrap": "Wyłącz zawijanie wierszy", "copy_code": "<PERSON><PERSON><PERSON><PERSON> kod"}}, "systemPromptWarning": "OSTRZEŻENIE: Aktywne niestandardowe zastąpienie instrukcji systemowych. <PERSON>że to poważnie zakłócić funkcjonalność i powodować nieprzewidywalne zachowanie.", "shellIntegration": {"title": "Ostrzeżenie wykonania polecenia", "description": "<PERSON>je polecenie jest wykonywane bez integracji powłoki terminala VSCode. Aby ukryć to ostrzeżenie, moż<PERSON>z wyłączyć integrację powłoki w sekcji <strong>Terminal</strong> w <settingsLink>ustawieniach Roo Code</settingsLink> lub rozwi<PERSON> problemy z integracją terminala VSCode korzystając z poniższego linku.", "troubleshooting": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby zobaczyć dokumentację integracji powłoki."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Osiągnięto limit automatycznie zatwierdzonych żądań", "description": "<PERSON><PERSON> o<PERSON>ął automatycznie zatwierdzony limit {{count}} żądania/żądań API. <PERSON>zy chcesz zresetować licznik i kontynuować zadanie?", "button": "Zresetuj i kontynuuj"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON> chce przeszukać bazę kodu w poszukiwaniu <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON> chce przeszuka<PERSON> bazę kodu w poszukiwaniu <code>{{query}}</code> w <code>{{path}}</code>:", "didSearch": "Znaleziono {{count}} wynik(ów) dla <code>{{query}}</code>:"}}