{"extension.displayName": "Roo Code (önceden Roo Cline)", "extension.description": "Düzenleyicinde tam bir AI ajanları geliştirme ekibi.", "command.newTask.title": "<PERSON><PERSON>", "command.explainCode.title": "Kodu Açıkla", "command.fixCode.title": "<PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON>tir", "command.addToContext.title": "Bağ<PERSON><PERSON>", "command.openInNewTab.title": "<PERSON><PERSON>", "command.focusInput.title": "<PERSON><PERSON><PERSON>", "command.setCustomStoragePath.title": "<PERSON><PERSON>", "command.terminal.addToContext.title": "Terminal İçeriğini Bağlama Ekle", "command.terminal.fixCommand.title": "<PERSON><PERSON> <PERSON><PERSON>", "command.terminal.explainCommand.title": "Bu Komutu Açıkla", "command.acceptInput.title": "Girişi/Öneriyi Kabul Et", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "command.mcpServers.title": "MCP Sunucuları", "command.prompts.title": "<PERSON><PERSON><PERSON>", "command.history.title": "Geçmiş", "command.openInEditor.title": "Düzenleyicide Aç", "command.settings.title": "<PERSON><PERSON><PERSON>", "command.documentation.title": "Dokümantasyon", "configuration.title": "Roo Code", "commands.allowedCommands.description": "'Her zaman yür<PERSON><PERSON><PERSON> işlemlerini onayla' etkinleştirildiğinde otomatik olarak yürütülebilen komutlar", "settings.vsCodeLmModelSelector.description": "VSCode dil modeli API'si için a<PERSON>", "settings.vsCodeLmModelSelector.vendor.description": "Dil modelinin <PERSON>ğlayıcısı (örn: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON>l modelinin a<PERSON> (örn: gpt-4)", "settings.customStoragePath.description": "Özel depolama yolu. Varsayılan konumu kullanmak için boş bırakın. Mutlak yolları destekler (örn: 'D:\\RooCodeStorage')"}