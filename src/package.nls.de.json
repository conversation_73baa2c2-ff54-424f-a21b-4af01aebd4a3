{"extension.displayName": "Roo Code (ehemals Roo Cline)", "extension.description": "Ein komplettes KI-Agenten-Entwicklungsteam in deinem Editor.", "command.newTask.title": "Neue Aufgabe", "command.explainCode.title": "Code Erklären", "command.fixCode.title": "Code Reparieren", "command.improveCode.title": "Code Verbessern", "command.addToContext.title": "Zum Kontext Hinzufügen", "command.openInNewTab.title": "In Neuem <PERSON>", "command.focusInput.title": "Eingabefeld Fokussieren", "command.setCustomStoragePath.title": "Benutzerdefinierten Speicherpfad Festlegen", "command.terminal.addToContext.title": "Terminal-Inhalt zum Kontext Hinzufügen", "command.terminal.fixCommand.title": "<PERSON><PERSON> Befehl Reparieren", "command.terminal.explainCommand.title": "Diesen Befehl Erklären", "command.acceptInput.title": "Eingabe/Vorschlag Akzeptieren", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "command.mcpServers.title": "MCP Server", "command.prompts.title": "Prompts", "command.history.title": "<PERSON><PERSON><PERSON><PERSON>", "command.openInEditor.title": "Im Editor <PERSON>", "command.settings.title": "Einstellungen", "command.documentation.title": "Dokumentation", "configuration.title": "Roo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON><PERSON>, die automatisch ausgeführt werden können, wenn 'Ausführungsoperationen immer genehmigen' aktiviert ist", "settings.vsCodeLmModelSelector.description": "Einstellungen für die VSCode-Sprachmodell-API", "settings.vsCodeLmModelSelector.vendor.description": "Der Anbieter des Sprachmodells (z.B. copilot)", "settings.vsCodeLmModelSelector.family.description": "Die Familie des Sprachmodells (z.B. gpt-4)", "settings.customStoragePath.description": "Benutzerdefinierter Speicherpfad. <PERSON><PERSON>, um den Standardspeicherort zu verwenden. Unterstützt absolute Pfade (z.B. 'D:\\RooCodeStorage')"}