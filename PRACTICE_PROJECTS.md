# Roo Code 学习实践项目集

> 基于学习路线图设计的渐进式实践项目，帮助巩固每个阶段的学习成果

## 🎯 项目概览

| 项目名称 | 对应阶段 | 难度等级 | 预计时间 | 核心技能 |
|---------|---------|---------|---------|---------|
| Mini Monorepo | 第1-2阶段 | 🟢 初级 | 3-5天 | Monorepo、设计模式 |
| Type-Safe Config | 第3阶段 | 🟡 中级 | 2-3天 | TypeScript、Zod |
| React State Manager | 第4阶段 | 🟡 中级 | 4-6天 | React、状态管理 |
| VS Code Extension | 第4阶段 | 🔴 高级 | 5-7天 | 扩展开发、Webview |
| Performance Optimizer | 第5阶段 | 🔴 高级 | 4-6天 | 算法、性能优化 |

---

## 🚀 项目1：Mini Monorepo - 简化版 AI 助手

**适用阶段**: 第1-2阶段完成后
**难度等级**: 🟢 初级
**预计时间**: 3-5天

### 项目目标
创建一个基础的 AI 助手框架，重点学习 Monorepo 架构和设计模式的实际应用。

### 技能要求
- [x] 完成第1阶段：项目结构和基础架构理解
- [x] 完成第2阶段：核心架构模式学习
- [ ] 基础的 Node.js 和 TypeScript 知识

### 项目结构
```
mini-ai-assistant/
├── 📁 packages/
│   ├── 📁 core/                 # 核心逻辑包
│   │   ├── 📄 package.json
│   │   ├── 📁 src/
│   │   │   ├── 📄 index.ts
│   │   │   ├── 📄 assistant.ts
│   │   │   └── 📄 types.ts
│   │   └── 📄 tsconfig.json
│   ├── 📁 providers/            # AI 提供商包
│   │   ├── 📄 package.json
│   │   ├── 📁 src/
│   │   │   ├── 📄 index.ts
│   │   │   ├── 📄 base-provider.ts
│   │   │   ├── 📄 mock-provider.ts
│   │   │   └── 📄 openai-provider.ts
│   │   └── 📄 tsconfig.json
│   └── 📁 types/                # 共享类型包
│       ├── 📄 package.json
│       ├── 📁 src/
│       │   ├── 📄 index.ts
│       │   ├── 📄 assistant.ts
│       │   └── 📄 provider.ts
│       └── 📄 tsconfig.json
├── 📁 apps/
│   └── 📁 cli/                  # 命令行应用
│       ├── 📄 package.json
│       ├── 📁 src/
│       │   ├── 📄 index.ts
│       │   └── 📄 commands.ts
│       └── 📄 tsconfig.json
├── 📄 package.json              # 根包配置
├── 📄 pnpm-workspace.yaml       # 工作区配置
├── 📄 turbo.json               # 构建配置
└── 📄 tsconfig.json            # 根 TS 配置
```

### 核心功能实现

#### 1. 工厂模式实现 (packages/providers/src/index.ts)
```typescript
import { BaseProvider } from './base-provider'
import { MockProvider } from './mock-provider'
import { OpenAIProvider } from './openai-provider'
import { ProviderConfig, ProviderType } from '@mini-ai/types'

export function createProvider(config: ProviderConfig): BaseProvider {
    switch (config.type) {
        case ProviderType.MOCK:
            return new MockProvider(config)
        case ProviderType.OPENAI:
            return new OpenAIProvider(config)
        default:
            throw new Error(`Unsupported provider type: ${config.type}`)
    }
}

export * from './base-provider'
export * from './mock-provider'
export * from './openai-provider'
```

#### 2. 抽象基类设计 (packages/providers/src/base-provider.ts)
```typescript
import { Message, Response, ProviderConfig } from '@mini-ai/types'

export abstract class BaseProvider {
    protected config: ProviderConfig

    constructor(config: ProviderConfig) {
        this.config = config
    }

    abstract async sendMessage(message: Message): Promise<Response>

    abstract async validateConfig(): Promise<boolean>

    protected formatMessage(message: Message): string {
        return `${message.role}: ${message.content}`
    }

    protected createResponse(content: string, metadata?: any): Response {
        return {
            content,
            metadata: {
                provider: this.config.type,
                timestamp: Date.now(),
                ...metadata
            }
        }
    }
}
```

#### 3. 具体实现示例 (packages/providers/src/mock-provider.ts)
```typescript
import { BaseProvider } from './base-provider'
import { Message, Response } from '@mini-ai/types'

export class MockProvider extends BaseProvider {
    async sendMessage(message: Message): Promise<Response> {
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        const responses = [
            "这是一个模拟回复。",
            "我理解您的问题。",
            "让我为您提供帮助。",
            "这是一个很好的问题。"
        ]

        const randomResponse = responses[Math.floor(Math.random() * responses.length)]

        return this.createResponse(randomResponse, {
            model: 'mock-model-v1',
            tokens: Math.floor(Math.random() * 100) + 50
        })
    }

    async validateConfig(): Promise<boolean> {
        return true // Mock provider 总是有效的
    }
}
```

### 学习重点

1. **Monorepo 架构实践**
   - 理解包之间的依赖关系
   - 掌握 pnpm workspace 的使用
   - 学习 Turbo 构建优化

2. **设计模式应用**
   - 工厂模式的实际实现
   - 抽象基类的设计原则
   - 策略模式的变体应用

3. **TypeScript 项目组织**
   - 类型定义的共享和复用
   - 模块导入导出的最佳实践
   - 编译配置的继承关系

### 验收标准

#### 功能验收
- [ ] 能够成功创建不同类型的 AI 提供商
- [ ] 命令行应用能够正常工作
- [ ] 所有包都能正确构建和测试
- [ ] 类型检查无错误

#### 代码质量验收
- [ ] 遵循 SOLID 原则
- [ ] 具有良好的错误处理
- [ ] 包含必要的单元测试
- [ ] 代码注释清晰完整

#### 架构验收
- [ ] 包依赖关系清晰合理
- [ ] 抽象层次设计恰当
- [ ] 易于扩展新的提供商
- [ ] 配置管理灵活可靠

### 扩展挑战

1. **添加新的提供商**
   ```typescript
   // 实现一个 Anthropic 提供商
   export class AnthropicProvider extends BaseProvider {
       // 实现具体逻辑
   }
   ```

2. **实现配置验证**
   ```typescript
   // 使用 Zod 进行配置验证
   const configSchema = z.object({
       type: z.enum(['mock', 'openai', 'anthropic']),
       apiKey: z.string().optional(),
       model: z.string().optional()
   })
   ```

3. **添加缓存机制**
   ```typescript
   // 实现简单的内存缓存
   export class CachedProvider extends BaseProvider {
       private cache = new Map<string, Response>()
       // 实现缓存逻辑
   }
   ```

### 学习资源

- [pnpm Workspaces 文档](https://pnpm.io/workspaces)
- [Turborepo 入门指南](https://turbo.build/repo/docs/getting-started)
- [TypeScript Project References](https://www.typescriptlang.org/docs/handbook/project-references.html)
- [设计模式 TypeScript 实现](https://refactoring.guru/design-patterns/typescript)

---

## 🔧 项目2：Type-Safe Config - 类型安全的配置系统

**适用阶段**: 第3阶段完成后
**难度等级**: 🟡 中级
**预计时间**: 2-3天

### 项目目标
实现一个完整的配置管理系统，重点学习 Zod + TypeScript 的类型安全设计。

### 技能要求
- [x] 完成第3阶段：TypeScript 类型安全设计
- [x] 理解 Zod schema 的基本使用
- [ ] 熟悉文件系统操作

### 项目结构
```
type-safe-config/
├── 📁 src/
│   ├── 📄 index.ts              # 主入口
│   ├── 📄 config-manager.ts     # 配置管理器
│   ├── 📄 schemas.ts            # Zod schemas
│   ├── 📄 types.ts              # TypeScript 类型
│   ├── 📄 validators.ts         # 自定义验证器
│   └── 📄 utils.ts              # 工具函数
├── 📁 examples/
│   ├── 📄 basic-usage.ts        # 基础使用示例
│   ├── 📄 advanced-usage.ts     # 高级使用示例
│   └── 📄 config-files/         # 示例配置文件
├── 📁 tests/
│   ├── 📄 config-manager.test.ts
│   ├── 📄 schemas.test.ts
│   └── 📄 validators.test.ts
├── 📄 package.json
├── 📄 tsconfig.json
└── 📄 README.md
```

### 核心功能实现

#### 1. Zod Schema 设计 (src/schemas.ts)
```typescript
import { z } from 'zod'

// 基础配置 schema
export const baseConfigSchema = z.object({
    name: z.string().min(1, "配置名称不能为空"),
    version: z.string().regex(/^\d+\.\d+\.\d+$/, "版本格式必须为 x.y.z"),
    description: z.string().optional(),
    enabled: z.boolean().default(true),
})

// 数据库配置 schema
export const databaseConfigSchema = z.object({
    host: z.string().min(1),
    port: z.number().int().min(1).max(65535),
    database: z.string().min(1),
    username: z.string().min(1),
    password: z.string().min(1),
    ssl: z.boolean().default(false),
    connectionTimeout: z.number().int().positive().default(5000),
})

// API 配置 schema
export const apiConfigSchema = z.object({
    baseUrl: z.string().url("必须是有效的 URL"),
    apiKey: z.string().min(1),
    timeout: z.number().int().positive().default(30000),
    retries: z.number().int().min(0).max(5).default(3),
    rateLimit: z.object({
        requests: z.number().int().positive(),
        window: z.number().int().positive(), // 时间窗口（毫秒）
    }).optional(),
})

// 完整应用配置 schema
export const appConfigSchema = z.object({
    ...baseConfigSchema.shape,
    database: databaseConfigSchema,
    api: apiConfigSchema,
    features: z.record(z.string(), z.boolean()).default({}),
    logging: z.object({
        level: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
        file: z.string().optional(),
        console: z.boolean().default(true),
    }).default({}),
})

// 类型推导
export type BaseConfig = z.infer<typeof baseConfigSchema>
export type DatabaseConfig = z.infer<typeof databaseConfigSchema>
export type ApiConfig = z.infer<typeof apiConfigSchema>
export type AppConfig = z.infer<typeof appConfigSchema>
```

#### 2. 泛型配置管理器 (src/config-manager.ts)
```typescript
import { z } from 'zod'
import * as fs from 'fs/promises'
import * as path from 'path'

export class ConfigManager<T extends z.ZodType> {
    private schema: T
    private configPath: string
    private config: z.infer<T> | null = null
    private watchers: Array<(config: z.infer<T>) => void> = []

    constructor(schema: T, configPath: string) {
        this.schema = schema
        this.configPath = path.resolve(configPath)
    }

    // 加载配置
    async load(): Promise<z.infer<T>> {
        try {
            const configData = await fs.readFile(this.configPath, 'utf-8')
            const rawConfig = JSON.parse(configData)

            // 使用 Zod 验证和转换
            const validatedConfig = this.schema.parse(rawConfig)
            this.config = validatedConfig

            // 通知观察者
            this.notifyWatchers(validatedConfig)

            return validatedConfig
        } catch (error) {
            if (error instanceof z.ZodError) {
                throw new ConfigValidationError('配置验证失败', error.errors)
            }
            throw new ConfigLoadError(`无法加载配置文件: ${error.message}`)
        }
    }

    // 保存配置
    async save(config: z.infer<T>): Promise<void> {
        try {
            // 验证配置
            const validatedConfig = this.schema.parse(config)

            // 确保目录存在
            await fs.mkdir(path.dirname(this.configPath), { recursive: true })

            // 保存配置
            await fs.writeFile(
                this.configPath,
                JSON.stringify(validatedConfig, null, 2),
                'utf-8'
            )

            this.config = validatedConfig
            this.notifyWatchers(validatedConfig)
        } catch (error) {
            if (error instanceof z.ZodError) {
                throw new ConfigValidationError('配置验证失败', error.errors)
            }
            throw new ConfigSaveError(`无法保存配置文件: ${error.message}`)
        }
    }

    // 获取当前配置
    get(): z.infer<T> | null {
        return this.config
    }

    // 部分更新配置
    async update(partialConfig: Partial<z.infer<T>>): Promise<z.infer<T>> {
        if (!this.config) {
            throw new Error('配置尚未加载，请先调用 load()')
        }

        const updatedConfig = { ...this.config, ...partialConfig }
        await this.save(updatedConfig)
        return updatedConfig
    }

    // 验证配置（不保存）
    validate(config: unknown): z.SafeParseResult<z.infer<T>> {
        return this.schema.safeParse(config)
    }

    // 监听配置变化
    watch(callback: (config: z.infer<T>) => void): () => void {
        this.watchers.push(callback)

        // 返回取消监听的函数
        return () => {
            const index = this.watchers.indexOf(callback)
            if (index > -1) {
                this.watchers.splice(index, 1)
            }
        }
    }

    private notifyWatchers(config: z.infer<T>): void {
        this.watchers.forEach(callback => {
            try {
                callback(config)
            } catch (error) {
                console.error('配置监听器执行失败:', error)
            }
        })
    }
}

// 自定义错误类
export class ConfigValidationError extends Error {
    constructor(message: string, public errors: z.ZodIssue[]) {
        super(message)
        this.name = 'ConfigValidationError'
    }
}

export class ConfigLoadError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'ConfigLoadError'
    }
}

export class ConfigSaveError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'ConfigSaveError'
    }
}
```

### 学习重点

1. **Zod Schema 设计**
   - 复杂对象的 schema 定义
   - 自定义验证规则
   - 默认值和可选字段处理

2. **泛型约束应用**
   - 泛型类的设计
   - 类型推导和类型安全
   - 条件类型的使用

3. **运行时类型验证**
   - 错误处理和用户友好的错误信息
   - 性能优化考虑
   - 验证结果的处理

### 验收标准

#### 功能验收
- [ ] 能够加载和保存 JSON 配置文件
- [ ] 配置验证错误信息清晰易懂
- [ ] 支持配置的部分更新
- [ ] 配置变化监听机制正常工作

#### 类型安全验收
- [ ] 所有配置操作都是类型安全的
- [ ] 编译时能够捕获类型错误
- [ ] 运行时验证与编译时类型一致
- [ ] 自动补全和类型提示完整

### 扩展挑战

1. **环境变量集成**
   ```typescript
   // 支持从环境变量覆盖配置
   const envOverrideSchema = z.object({
       DATABASE_HOST: z.string().optional(),
       API_KEY: z.string().optional(),
   })
   ```

2. **配置热重载**
   ```typescript
   // 监听文件变化并自动重新加载
   import chokidar from 'chokidar'

   class HotReloadConfigManager extends ConfigManager {
       // 实现文件监听逻辑
   }
   ```

3. **配置加密**
   ```typescript
   // 敏感配置的加密存储
   const encryptedConfigSchema = z.object({
       encrypted: z.string(),
       salt: z.string(),
   })
   ```

---

## ⚛️ 项目3：React State Manager - 复杂状态管理系统

**适用阶段**: 第4阶段完成后
**难度等级**: 🟡 中级
**预计时间**: 4-6天

### 项目目标
实现一个类似 Redux 但更简单的状态管理库，学习 React Context、Reducer 模式和性能优化。

### 技能要求
- [x] 完成第4阶段：前端组件和状态管理
- [x] 熟悉 React Hooks 和 Context API
- [ ] 理解 Reducer 模式

### 项目结构
```
react-state-manager/
├── 📁 src/
│   ├── 📁 core/
│   │   ├── 📄 store.ts          # 核心 Store 实现
│   │   ├── 📄 context.ts        # React Context 封装
│   │   ├── 📄 hooks.ts          # 自定义 Hooks
│   │   └── 📄 types.ts          # 类型定义
│   ├── 📁 middleware/
│   │   ├── 📄 logger.ts         # 日志中间件
│   │   ├── 📄 persist.ts        # 持久化中间件
│   │   └── 📄 devtools.ts       # 开发工具中间件
│   ├── 📁 utils/
│   │   ├── 📄 immutable.ts      # 不可变更新工具
│   │   └── 📄 selector.ts       # 选择器工具
│   └── 📄 index.ts              # 主入口
├── 📁 examples/
│   ├── 📁 todo-app/             # Todo 应用示例
│   ├── 📁 counter-app/          # 计数器示例
│   └── 📁 async-app/            # 异步操作示例
├── 📁 tests/
└── 📄 package.json
```

### 核心功能实现

#### 1. 核心 Store 实现 (src/core/store.ts)
```typescript
import { Reducer, Dispatch } from 'react'

export interface Action<T = any> {
    type: string
    payload?: T
}

export interface Middleware<S = any> {
    (store: Store<S>): (next: Dispatch<Action>) => (action: Action) => void
}

export interface StoreConfig<S> {
    reducer: Reducer<S, Action>
    initialState: S
    middleware?: Middleware<S>[]
    enhancers?: StoreEnhancer<S>[]
}

export interface StoreEnhancer<S> {
    (createStore: StoreCreator): StoreCreator
}

export interface StoreCreator {
    <S>(config: StoreConfig<S>): Store<S>
}

export interface Store<S = any> {
    getState(): S
    dispatch(action: Action): void
    subscribe(listener: () => void): () => void
    replaceReducer(nextReducer: Reducer<S, Action>): void
}

export class SimpleStore<S> implements Store<S> {
    private state: S
    private reducer: Reducer<S, Action>
    private listeners: Set<() => void> = new Set()
    private middleware: Middleware<S>[]
    private dispatch: Dispatch<Action>

    constructor(config: StoreConfig<S>) {
        this.state = config.initialState
        this.reducer = config.reducer
        this.middleware = config.middleware || []

        // 构建中间件链
        this.dispatch = this.applyMiddleware()
    }

    getState(): S {
        return this.state
    }

    dispatch(action: Action): void {
        // 这个方法会被中间件链替换
        this.state = this.reducer(this.state, action)
        this.notifyListeners()
    }

    subscribe(listener: () => void): () => void {
        this.listeners.add(listener)

        return () => {
            this.listeners.delete(listener)
        }
    }

    replaceReducer(nextReducer: Reducer<S, Action>): void {
        this.reducer = nextReducer
        this.dispatch({ type: '@@REPLACE_REDUCER' })
    }

    private applyMiddleware(): Dispatch<Action> {
        if (this.middleware.length === 0) {
            return this.dispatch.bind(this)
        }

        const middlewareAPI = {
            getState: this.getState.bind(this),
            dispatch: (action: Action) => this.dispatch(action)
        }

        const chain = this.middleware.map(middleware => middleware(middlewareAPI))

        return chain.reduce(
            (composed, middleware) => middleware(composed),
            this.baseDispatch.bind(this)
        )
    }

    private baseDispatch(action: Action): void {
        this.state = this.reducer(this.state, action)
        this.notifyListeners()
    }

    private notifyListeners(): void {
        this.listeners.forEach(listener => {
            try {
                listener()
            } catch (error) {
                console.error('Store listener error:', error)
            }
        })
    }
}

export function createStore<S>(config: StoreConfig<S>): Store<S> {
    return new SimpleStore(config)
}
```

#### 2. React Context 封装 (src/core/context.ts)
```typescript
import React, { createContext, useContext, useReducer, ReactNode, Reducer } from 'react'
import { Action, Store, createStore, StoreConfig } from './store'

interface StoreContextValue<S> {
    store: Store<S>
}

export function createStoreContext<S>() {
    const StoreContext = createContext<StoreContextValue<S> | null>(null)

    interface StoreProviderProps {
        config: StoreConfig<S>
        children: ReactNode
    }

    function StoreProvider({ config, children }: StoreProviderProps) {
        const [store] = React.useState(() => createStore(config))

        return (
            <StoreContext.Provider value={{ store }}>
                {children}
            </StoreContext.Provider>
        )
    }

    function useStore(): Store<S> {
        const context = useContext(StoreContext)
        if (!context) {
            throw new Error('useStore must be used within a StoreProvider')
        }
        return context.store
    }

    return {
        StoreProvider,
        useStore,
        StoreContext
    }
}

// 通用的 Provider 组件
export interface ProviderProps<S> {
    reducer: Reducer<S, Action>
    initialState: S
    children: ReactNode
}

export function createProvider<S>() {
    const { StoreProvider, useStore } = createStoreContext<S>()

    function Provider({ reducer, initialState, children }: ProviderProps<S>) {
        return (
            <StoreProvider config={{ reducer, initialState }}>
                {children}
            </StoreProvider>
        )
    }

    return {
        Provider,
        useStore
    }
}
```

### 学习重点

1. **React Context 模式**
   - Context 的创建和使用
   - Provider 组件的设计
   - Context 性能优化

2. **Reducer 模式**
   - 状态更新的不可变性
   - Action 的设计原则
   - 复杂状态的管理

3. **自定义 Hooks**
   - Hook 的组合和复用
   - 性能优化技巧
   - 错误处理

### 验收标准

#### 功能验收
- [ ] 状态管理功能完整可用
- [ ] 支持中间件扩展
- [ ] 组件能够正确订阅状态变化
- [ ] 性能表现良好

#### 代码质量验收
- [ ] 类型安全完整
- [ ] 错误处理完善
- [ ] 代码结构清晰
- [ ] 测试覆盖率高

### 扩展挑战

1. **选择器优化**
   ```typescript
   // 实现记忆化选择器
   export function createSelector<S, R>(
       selector: (state: S) => R,
       equalityFn?: (a: R, b: R) => boolean
   ): (state: S) => R {
       let lastState: S
       let lastResult: R

       return (state: S): R => {
           if (state !== lastState) {
               const newResult = selector(state)
               if (!equalityFn || !equalityFn(lastResult, newResult)) {
                   lastResult = newResult
               }
               lastState = state
           }
           return lastResult
       }
   }
   ```

2. **异步 Action 支持**
   ```typescript
   // 实现 Thunk 中间件
   export const thunkMiddleware: Middleware = (store) => (next) => (action) => {
       if (typeof action === 'function') {
           return action(store.dispatch, store.getState)
       }
       return next(action)
   }
   ```

3. **时间旅行调试**
   ```typescript
   // 实现状态历史记录
   interface StateHistory<S> {
       past: S[]
       present: S
       future: S[]
   }
   ```

---

## 🖥️ 项目4：VS Code Extension - 代码助手扩展

**适用阶段**: 第4阶段完成后
**难度等级**: 🔴 高级
**预计时间**: 5-7天

### 项目目标
开发一个完整的 VS Code 扩展，集成 Webview、命令、设置等功能，学习扩展开发的完整流程。

### 技能要求
- [x] 完成第4阶段：前端组件和状态管理
- [x] 熟悉 React 和 TypeScript
- [ ] 了解 VS Code Extension API

### 项目结构
```
vscode-code-assistant/
├── 📁 src/
│   ├── 📁 extension/            # 扩展主体代码
│   │   ├── 📄 extension.ts      # 扩展入口
│   │   ├── 📄 commands.ts       # 命令处理
│   │   ├── 📄 providers.ts      # 各种 Provider
│   │   └── 📄 webview.ts        # Webview 管理
│   ├── 📁 webview/              # React Webview 代码
│   │   ├── 📁 components/       # React 组件
│   │   ├── 📁 hooks/            # 自定义 Hooks
│   │   ├── 📁 utils/            # 工具函数
│   │   └── 📄 index.tsx         # Webview 入口
│   ├── 📁 shared/               # 共享类型和工具
│   │   ├── 📄 types.ts          # 类型定义
│   │   └── 📄 messages.ts       # 消息协议
│   └── 📁 test/                 # 测试文件
├── 📁 media/                    # 静态资源
├── 📁 syntaxes/                 # 语法高亮定义
├── 📄 package.json              # 扩展清单
├── 📄 webpack.config.js         # 构建配置
└── 📄 tsconfig.json
```

### 核心功能实现

#### 1. 扩展入口 (src/extension/extension.ts)
```typescript
import * as vscode from 'vscode'
import { CodeAssistantProvider } from './providers'
import { registerCommands } from './commands'
import { WebviewManager } from './webview'

export function activate(context: vscode.ExtensionContext) {
    console.log('Code Assistant 扩展已激活')

    // 创建 Webview 管理器
    const webviewManager = new WebviewManager(context)

    // 注册侧边栏 Provider
    const provider = new CodeAssistantProvider(context, webviewManager)
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            CodeAssistantProvider.viewType,
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        )
    )

    // 注册命令
    registerCommands(context, webviewManager)

    // 注册配置变化监听
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('codeAssistant')) {
                webviewManager.updateConfiguration()
            }
        })
    )

    // 注册文档变化监听
    context.subscriptions.push(
        vscode.workspace.onDidChangeTextDocument(e => {
            webviewManager.notifyDocumentChange(e)
        })
    )
}

export function deactivate() {
    console.log('Code Assistant 扩展已停用')
}
```

#### 2. Webview Provider (src/extension/providers.ts)
```typescript
import * as vscode from 'vscode'
import { WebviewManager } from './webview'

export class CodeAssistantProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'codeAssistant.chatView'

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly webviewManager: WebviewManager
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        token: vscode.CancellationToken,
    ) {
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this.context.extensionUri
            ]
        }

        // 设置 HTML 内容
        webviewView.webview.html = this.getHtmlForWebview(webviewView.webview)

        // 设置消息处理
        this.webviewManager.setupWebview(webviewView.webview)

        // 监听可见性变化
        webviewView.onDidChangeVisibility(() => {
            if (webviewView.visible) {
                this.webviewManager.onWebviewVisible()
            }
        })
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        // 获取资源 URI
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'out', 'webview.js')
        )
        const styleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'out', 'webview.css')
        )

        // 生成 nonce 用于 CSP
        const nonce = getNonce()

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy"
                  content="default-src 'none';
                           style-src ${webview.cspSource} 'unsafe-inline';
                           script-src 'nonce-${nonce}';">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleUri}" rel="stylesheet">
            <title>Code Assistant</title>
        </head>
        <body>
            <div id="root"></div>
            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`
    }
}

function getNonce(): string {
    let text = ''
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length))
    }
    return text
}
```

#### 3. Webview 管理器 (src/extension/webview.ts)
```typescript
import * as vscode from 'vscode'
import { ExtensionMessage, WebviewMessage } from '../shared/messages'

export class WebviewManager {
    private webview: vscode.Webview | undefined
    private disposables: vscode.Disposable[] = []

    constructor(private readonly context: vscode.ExtensionContext) {}

    public setupWebview(webview: vscode.Webview) {
        this.webview = webview

        // 监听来自 Webview 的消息
        this.disposables.push(
            webview.onDidReceiveMessage(async (message: WebviewMessage) => {
                await this.handleWebviewMessage(message)
            })
        )

        // 发送初始状态
        this.sendInitialState()
    }

    private async handleWebviewMessage(message: WebviewMessage) {
        switch (message.type) {
            case 'getActiveDocument':
                await this.handleGetActiveDocument()
                break

            case 'insertCode':
                await this.handleInsertCode(message.payload)
                break

            case 'analyzeCode':
                await this.handleAnalyzeCode(message.payload)
                break

            case 'getConfiguration':
                await this.handleGetConfiguration()
                break

            default:
                console.warn('未知的 Webview 消息类型:', message.type)
        }
    }

    private async handleGetActiveDocument() {
        const editor = vscode.window.activeTextEditor
        if (editor) {
            const document = editor.document
            const selection = editor.selection

            this.postMessage({
                type: 'activeDocument',
                payload: {
                    fileName: document.fileName,
                    languageId: document.languageId,
                    content: document.getText(),
                    selection: {
                        start: document.offsetAt(selection.start),
                        end: document.offsetAt(selection.end),
                        text: document.getText(selection)
                    }
                }
            })
        } else {
            this.postMessage({
                type: 'activeDocument',
                payload: null
            })
        }
    }

    private async handleInsertCode(payload: { code: string; position?: 'cursor' | 'end' }) {
        const editor = vscode.window.activeTextEditor
        if (!editor) {
            vscode.window.showErrorMessage('没有活动的编辑器')
            return
        }

        const position = payload.position === 'end'
            ? new vscode.Position(editor.document.lineCount, 0)
            : editor.selection.active

        await editor.edit(editBuilder => {
            editBuilder.insert(position, payload.code)
        })

        // 格式化插入的代码
        await vscode.commands.executeCommand('editor.action.formatDocument')
    }

    private async handleAnalyzeCode(payload: { code: string }) {
        // 这里可以集成代码分析逻辑
        // 例如：语法检查、复杂度分析、建议等

        const analysis = {
            lineCount: payload.code.split('\n').length,
            complexity: this.calculateComplexity(payload.code),
            suggestions: this.generateSuggestions(payload.code)
        }

        this.postMessage({
            type: 'codeAnalysis',
            payload: analysis
        })
    }

    private calculateComplexity(code: string): number {
        // 简单的复杂度计算
        const complexityKeywords = ['if', 'else', 'for', 'while', 'switch', 'case', 'try', 'catch']
        let complexity = 1

        complexityKeywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g')
            const matches = code.match(regex)
            if (matches) {
                complexity += matches.length
            }
        })

        return complexity
    }

    private generateSuggestions(code: string): string[] {
        const suggestions: string[] = []

        // 检查常见问题
        if (code.includes('console.log')) {
            suggestions.push('考虑移除调试用的 console.log 语句')
        }

        if (code.length > 1000) {
            suggestions.push('函数过长，考虑拆分为更小的函数')
        }

        if (!/\/\*[\s\S]*?\*\/|\/\/.*$/m.test(code)) {
            suggestions.push('添加注释以提高代码可读性')
        }

        return suggestions
    }

    public postMessage(message: ExtensionMessage) {
        if (this.webview) {
            this.webview.postMessage(message)
        }
    }

    public updateConfiguration() {
        const config = vscode.workspace.getConfiguration('codeAssistant')
        this.postMessage({
            type: 'configurationUpdate',
            payload: {
                enabled: config.get('enabled', true),
                autoAnalyze: config.get('autoAnalyze', false),
                theme: config.get('theme', 'auto')
            }
        })
    }

    public notifyDocumentChange(event: vscode.TextDocumentChangeEvent) {
        if (event.document === vscode.window.activeTextEditor?.document) {
            this.postMessage({
                type: 'documentChange',
                payload: {
                    fileName: event.document.fileName,
                    changes: event.contentChanges.map(change => ({
                        range: {
                            start: event.document.offsetAt(change.range.start),
                            end: event.document.offsetAt(change.range.end)
                        },
                        text: change.text
                    }))
                }
            })
        }
    }

    public onWebviewVisible() {
        // Webview 变为可见时的处理
        this.sendInitialState()
    }

    private sendInitialState() {
        this.handleGetActiveDocument()
        this.updateConfiguration()
    }

    public dispose() {
        this.disposables.forEach(d => d.dispose())
    }
}
```

#### 4. React Webview 组件 (src/webview/components/ChatInterface.tsx)
```typescript
import React, { useState, useEffect, useRef } from 'react'
import { ExtensionMessage, WebviewMessage } from '../../shared/messages'

interface Message {
    id: string
    type: 'user' | 'assistant'
    content: string
    timestamp: Date
}

export const ChatInterface: React.FC = () => {
    const [messages, setMessages] = useState<Message[]>([])
    const [input, setInput] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [activeDocument, setActiveDocument] = useState<any>(null)
    const messagesEndRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        // 监听来自扩展的消息
        const handleMessage = (event: MessageEvent<ExtensionMessage>) => {
            const message = event.data

            switch (message.type) {
                case 'activeDocument':
                    setActiveDocument(message.payload)
                    break

                case 'codeAnalysis':
                    handleCodeAnalysis(message.payload)
                    break

                case 'configurationUpdate':
                    // 处理配置更新
                    break
            }
        }

        window.addEventListener('message', handleMessage)

        // 请求初始数据
        postMessage({ type: 'getActiveDocument' })

        return () => {
            window.removeEventListener('message', handleMessage)
        }
    }, [])

    useEffect(() => {
        scrollToBottom()
    }, [messages])

    const postMessage = (message: WebviewMessage) => {
        // @ts-ignore
        vscode.postMessage(message)
    }

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
    }

    const handleSendMessage = async () => {
        if (!input.trim() || isLoading) return

        const userMessage: Message = {
            id: Date.now().toString(),
            type: 'user',
            content: input,
            timestamp: new Date()
        }

        setMessages(prev => [...prev, userMessage])
        setInput('')
        setIsLoading(true)

        try {
            // 处理用户输入
            await processUserInput(input)
        } catch (error) {
            console.error('处理消息失败:', error)
            addAssistantMessage('抱歉，处理您的请求时出现了错误。')
        } finally {
            setIsLoading(false)
        }
    }

    const processUserInput = async (input: string) => {
        const lowerInput = input.toLowerCase()

        if (lowerInput.includes('分析') || lowerInput.includes('analyze')) {
            if (activeDocument?.selection?.text) {
                postMessage({
                    type: 'analyzeCode',
                    payload: { code: activeDocument.selection.text }
                })
            } else {
                addAssistantMessage('请先选择要分析的代码。')
            }
        } else if (lowerInput.includes('插入') || lowerInput.includes('insert')) {
            // 处理代码插入请求
            const codeMatch = input.match(/```[\s\S]*?```/)
            if (codeMatch) {
                const code = codeMatch[0].replace(/```\w*\n?/g, '').replace(/```/g, '')
                postMessage({
                    type: 'insertCode',
                    payload: { code }
                })
                addAssistantMessage('代码已插入到编辑器中。')
            } else {
                addAssistantMessage('请提供要插入的代码，使用 ```代码``` 格式。')
            }
        } else {
            // 通用回复
            addAssistantMessage('我是您的代码助手。您可以：\n- 说"分析代码"来分析选中的代码\n- 使用 ```代码``` 格式来插入代码\n- 询问编程相关问题')
        }
    }

    const addAssistantMessage = (content: string) => {
        const assistantMessage: Message = {
            id: Date.now().toString(),
            type: 'assistant',
            content,
            timestamp: new Date()
        }
        setMessages(prev => [...prev, assistantMessage])
    }

    const handleCodeAnalysis = (analysis: any) => {
        const content = `代码分析结果：

📊 **统计信息**
- 行数: ${analysis.lineCount}
- 复杂度: ${analysis.complexity}

💡 **建议**
${analysis.suggestions.map((s: string) => `- ${s}`).join('\n')}`

        addAssistantMessage(content)
    }

    return (
        <div className="chat-interface">
            <div className="messages-container">
                {messages.map(message => (
                    <div key={message.id} className={`message ${message.type}`}>
                        <div className="message-content">
                            {message.content}
                        </div>
                        <div className="message-time">
                            {message.timestamp.toLocaleTimeString()}
                        </div>
                    </div>
                ))}
                {isLoading && (
                    <div className="message assistant loading">
                        <div className="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                )}
                <div ref={messagesEndRef} />
            </div>

            <div className="input-container">
                <textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault()
                            handleSendMessage()
                        }
                    }}
                    placeholder="输入您的问题或命令..."
                    disabled={isLoading}
                />
                <button
                    onClick={handleSendMessage}
                    disabled={!input.trim() || isLoading}
                >
                    发送
                </button>
            </div>

            {activeDocument && (
                <div className="document-info">
                    <small>
                        当前文件: {activeDocument.fileName?.split('/').pop() || '未知'}
                        {activeDocument.selection?.text &&
                            ` (已选择 ${activeDocument.selection.text.length} 个字符)`
                        }
                    </small>
                </div>
            )}
        </div>
    )
}
```

### 学习重点

1. **VS Code Extension API**
   - 扩展生命周期管理
   - 命令注册和处理
   - Webview 创建和管理
   - 配置系统集成

2. **Webview 通信机制**
   - 消息协议设计
   - 类型安全的通信
   - 错误处理和重连

3. **React 在 Webview 中的应用**
   - 受限环境下的 React 开发
   - 性能优化考虑
   - 状态管理策略

### 验收标准

#### 功能验收
- [ ] 扩展能够正常安装和激活
- [ ] Webview 界面正常显示和交互
- [ ] 命令执行正确无误
- [ ] 配置系统工作正常

#### 用户体验验收
- [ ] 界面响应流畅
- [ ] 错误提示友好
- [ ] 功能易于发现和使用
- [ ] 与 VS Code 主题一致

#### 代码质量验收
- [ ] 类型安全完整
- [ ] 错误处理完善
- [ ] 内存泄漏检查通过
- [ ] 性能表现良好

### 扩展挑战

1. **语法高亮支持**
   ```json
   // 在 package.json 中添加语法定义
   "contributes": {
     "grammars": [{
       "language": "custom-lang",
       "scopeName": "source.custom",
       "path": "./syntaxes/custom.tmGrammar.json"
     }]
   }
   ```

2. **代码补全提供者**
   ```typescript
   // 实现自定义补全
   export class CustomCompletionProvider implements vscode.CompletionItemProvider {
     provideCompletionItems(
       document: vscode.TextDocument,
       position: vscode.Position
     ): vscode.CompletionItem[] {
       // 实现补全逻辑
     }
   }
   ```

3. **调试适配器**
   ```typescript
   // 实现调试功能
   export class CustomDebugAdapterDescriptorFactory
     implements vscode.DebugAdapterDescriptorFactory {
     // 实现调试适配器
   }
   ```

## 🚀 项目5：Performance Optimizer - 性能优化工具

**适用阶段**: 第5阶段完成后
**难度等级**: 🔴 高级
**预计时间**: 4-6天

### 项目目标
创建一个代码性能分析和优化工具，重点学习算法优化、并发处理和性能监控技术。

### 技能要求
- [x] 完成第5阶段：算法和性能优化
- [x] 熟悉算法分析和优化技巧
- [ ] 了解性能监控工具

### 项目结构
```
performance-optimizer/
├── 📁 src/
│   ├── 📁 analyzers/            # 分析器模块
│   │   ├── 📄 complexity.ts     # 复杂度分析
│   │   ├── 📄 memory.ts         # 内存使用分析
│   │   ├── 📄 performance.ts    # 性能瓶颈分析
│   │   └── 📄 dependencies.ts   # 依赖分析
│   ├── 📁 optimizers/           # 优化器模块
│   │   ├── 📄 code-optimizer.ts # 代码优化
│   │   ├── 📄 bundle-optimizer.ts # 打包优化
│   │   └── 📄 cache-optimizer.ts # 缓存优化
│   ├── 📁 algorithms/           # 核心算法
│   │   ├── 📄 search.ts         # 搜索算法
│   │   ├── 📄 sort.ts           # 排序算法
│   │   ├── 📄 graph.ts          # 图算法
│   │   └── 📄 dynamic-programming.ts # 动态规划
│   ├── 📁 utils/                # 工具函数
│   │   ├── 📄 ast-parser.ts     # AST 解析
│   │   ├── 📄 metrics.ts        # 性能指标
│   │   └── 📄 reporter.ts       # 报告生成
│   └── 📄 index.ts              # 主入口
├── 📁 benchmarks/               # 性能基准测试
├── 📁 examples/                 # 示例代码
└── 📁 tests/                    # 测试文件
```

### 核心功能实现

#### 1. 复杂度分析器 (src/analyzers/complexity.ts)
```typescript
import * as ts from 'typescript'

export interface ComplexityMetrics {
    cyclomaticComplexity: number
    cognitiveComplexity: number
    nestingDepth: number
    linesOfCode: number
    maintainabilityIndex: number
}

export class ComplexityAnalyzer {
    private sourceFile: ts.SourceFile

    constructor(code: string, fileName: string = 'temp.ts') {
        this.sourceFile = ts.createSourceFile(
            fileName,
            code,
            ts.ScriptTarget.Latest,
            true
        )
    }

    public analyze(): ComplexityMetrics {
        const visitor = new ComplexityVisitor()
        visitor.visit(this.sourceFile)

        return {
            cyclomaticComplexity: visitor.getCyclomaticComplexity(),
            cognitiveComplexity: visitor.getCognitiveComplexity(),
            nestingDepth: visitor.getMaxNestingDepth(),
            linesOfCode: visitor.getLinesOfCode(),
            maintainabilityIndex: this.calculateMaintainabilityIndex(visitor)
        }
    }

    private calculateMaintainabilityIndex(visitor: ComplexityVisitor): number {
        const halsteadVolume = visitor.getHalsteadVolume()
        const cyclomaticComplexity = visitor.getCyclomaticComplexity()
        const linesOfCode = visitor.getLinesOfCode()

        // 维护性指数计算公式
        const maintainabilityIndex = Math.max(0,
            (171 - 5.2 * Math.log(halsteadVolume) -
             0.23 * cyclomaticComplexity -
             16.2 * Math.log(linesOfCode)) * 100 / 171
        )

        return Math.round(maintainabilityIndex * 100) / 100
    }
}

class ComplexityVisitor {
    private cyclomaticComplexity = 1 // 基础复杂度
    private cognitiveComplexity = 0
    private currentNestingDepth = 0
    private maxNestingDepth = 0
    private linesOfCode = 0
    private operators = new Set<string>()
    private operands = new Set<string>()

    public visit(node: ts.Node): void {
        this.updateMetrics(node)
        ts.forEachChild(node, child => this.visit(child))
    }

    private updateMetrics(node: ts.Node): void {
        // 更新圈复杂度
        if (this.isComplexityIncreasingNode(node)) {
            this.cyclomaticComplexity++
        }

        // 更新认知复杂度
        if (this.isCognitiveComplexityIncreasingNode(node)) {
            this.cognitiveComplexity += this.getCognitiveComplexityIncrement(node)
        }

        // 更新嵌套深度
        if (this.isNestingNode(node)) {
            this.currentNestingDepth++
            this.maxNestingDepth = Math.max(this.maxNestingDepth, this.currentNestingDepth)
        }

        // 更新 Halstead 指标
        this.updateHalsteadMetrics(node)

        // 更新代码行数
        if (node.kind === ts.SyntaxKind.SourceFile) {
            this.linesOfCode = (node as ts.SourceFile).getLineAndCharacterOfPosition(
                (node as ts.SourceFile).getFullText().length
            ).line + 1
        }
    }

    private isComplexityIncreasingNode(node: ts.Node): boolean {
        return [
            ts.SyntaxKind.IfStatement,
            ts.SyntaxKind.WhileStatement,
            ts.SyntaxKind.ForStatement,
            ts.SyntaxKind.ForInStatement,
            ts.SyntaxKind.ForOfStatement,
            ts.SyntaxKind.DoStatement,
            ts.SyntaxKind.SwitchStatement,
            ts.SyntaxKind.CaseClause,
            ts.SyntaxKind.CatchClause,
            ts.SyntaxKind.ConditionalExpression,
            ts.SyntaxKind.BinaryExpression // && 和 || 操作符
        ].includes(node.kind)
    }

    private isCognitiveComplexityIncreasingNode(node: ts.Node): boolean {
        return [
            ts.SyntaxKind.IfStatement,
            ts.SyntaxKind.WhileStatement,
            ts.SyntaxKind.ForStatement,
            ts.SyntaxKind.SwitchStatement,
            ts.SyntaxKind.CatchClause
        ].includes(node.kind)
    }

    private getCognitiveComplexityIncrement(node: ts.Node): number {
        // 认知复杂度会根据嵌套深度增加
        return 1 + this.currentNestingDepth
    }

    private isNestingNode(node: ts.Node): boolean {
        return [
            ts.SyntaxKind.IfStatement,
            ts.SyntaxKind.WhileStatement,
            ts.SyntaxKind.ForStatement,
            ts.SyntaxKind.FunctionDeclaration,
            ts.SyntaxKind.MethodDeclaration,
            ts.SyntaxKind.TryStatement
        ].includes(node.kind)
    }

    private updateHalsteadMetrics(node: ts.Node): void {
        // 简化的 Halstead 指标计算
        if (ts.isToken(node)) {
            const text = node.getText()
            if (this.isOperator(node.kind)) {
                this.operators.add(text)
            } else if (this.isOperand(node.kind)) {
                this.operands.add(text)
            }
        }
    }

    private isOperator(kind: ts.SyntaxKind): boolean {
        return [
            ts.SyntaxKind.PlusToken,
            ts.SyntaxKind.MinusToken,
            ts.SyntaxKind.AsteriskToken,
            ts.SyntaxKind.SlashToken,
            ts.SyntaxKind.EqualsEqualsToken,
            ts.SyntaxKind.ExclamationEqualsToken,
            ts.SyntaxKind.LessThanToken,
            ts.SyntaxKind.GreaterThanToken
        ].includes(kind)
    }

    private isOperand(kind: ts.SyntaxKind): boolean {
        return [
            ts.SyntaxKind.Identifier,
            ts.SyntaxKind.NumericLiteral,
            ts.SyntaxKind.StringLiteral
        ].includes(kind)
    }

    public getCyclomaticComplexity(): number {
        return this.cyclomaticComplexity
    }

    public getCognitiveComplexity(): number {
        return this.cognitiveComplexity
    }

    public getMaxNestingDepth(): number {
        return this.maxNestingDepth
    }

    public getLinesOfCode(): number {
        return this.linesOfCode
    }

    public getHalsteadVolume(): number {
        const n1 = this.operators.size // 不同操作符数量
        const n2 = this.operands.size  // 不同操作数数量
        const vocabulary = n1 + n2
        const length = vocabulary * 2 // 简化计算

        return length * Math.log2(vocabulary)
    }
}
```

#### 2. 性能优化搜索算法 (src/algorithms/search.ts)
```typescript
export interface SearchResult<T> {
    item: T
    score: number
    index: number
}

export class OptimizedSearch<T> {
    private items: T[]
    private indexedItems: Map<string, T[]> = new Map()
    private searchCache: Map<string, SearchResult<T>[]> = new Map()

    constructor(items: T[], private keyExtractor: (item: T) => string) {
        this.items = items
        this.buildIndex()
    }

    // 构建倒排索引以提高搜索性能
    private buildIndex(): void {
        this.items.forEach((item, index) => {
            const key = this.keyExtractor(item)
            const words = this.tokenize(key)

            words.forEach(word => {
                if (!this.indexedItems.has(word)) {
                    this.indexedItems.set(word, [])
                }
                this.indexedItems.get(word)!.push(item)
            })
        })
    }

    private tokenize(text: string): string[] {
        return text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 0)
    }

    // 模糊搜索算法 - 使用 Levenshtein 距离
    public fuzzySearch(query: string, maxResults: number = 10): SearchResult<T>[] {
        const cacheKey = `${query}:${maxResults}`
        if (this.searchCache.has(cacheKey)) {
            return this.searchCache.get(cacheKey)!
        }

        const queryWords = this.tokenize(query)
        const candidates = new Map<T, number>()

        // 使用索引快速找到候选项
        queryWords.forEach(queryWord => {
            this.indexedItems.forEach((items, indexWord) => {
                const distance = this.levenshteinDistance(queryWord, indexWord)
                const maxDistance = Math.floor(queryWord.length * 0.3) // 允许30%的错误

                if (distance <= maxDistance) {
                    const score = 1 - (distance / queryWord.length)
                    items.forEach(item => {
                        const currentScore = candidates.get(item) || 0
                        candidates.set(item, currentScore + score)
                    })
                }
            })
        })

        // 转换为结果数组并排序
        const results: SearchResult<T>[] = Array.from(candidates.entries())
            .map(([item, score]) => ({
                item,
                score,
                index: this.items.indexOf(item)
            }))
            .sort((a, b) => b.score - a.score)
            .slice(0, maxResults)

        // 缓存结果
        this.searchCache.set(cacheKey, results)
        return results
    }

    // 优化的 Levenshtein 距离算法
    private levenshteinDistance(str1: string, str2: string): number {
        const matrix: number[][] = []
        const len1 = str1.length
        const len2 = str2.length

        // 如果其中一个字符串为空
        if (len1 === 0) return len2
        if (len2 === 0) return len1

        // 初始化矩阵
        for (let i = 0; i <= len1; i++) {
            matrix[i] = [i]
        }
        for (let j = 0; j <= len2; j++) {
            matrix[0][j] = j
        }

        // 填充矩阵
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,     // 删除
                    matrix[i][j - 1] + 1,     // 插入
                    matrix[i - 1][j - 1] + cost // 替换
                )
            }
        }

        return matrix[len1][len2]
    }

    // 二分搜索（适用于已排序数据）
    public binarySearch(
        target: T,
        compareFn: (a: T, b: T) => number
    ): SearchResult<T> | null {
        let left = 0
        let right = this.items.length - 1

        while (left <= right) {
            const mid = Math.floor((left + right) / 2)
            const comparison = compareFn(this.items[mid], target)

            if (comparison === 0) {
                return {
                    item: this.items[mid],
                    score: 1.0,
                    index: mid
                }
            } else if (comparison < 0) {
                left = mid + 1
            } else {
                right = mid - 1
            }
        }

        return null
    }

    // 清理缓存
    public clearCache(): void {
        this.searchCache.clear()
    }

    // 获取搜索统计信息
    public getStats(): {
        totalItems: number
        indexSize: number
        cacheSize: number
    } {
        return {
            totalItems: this.items.length,
            indexSize: this.indexedItems.size,
            cacheSize: this.searchCache.size
        }
    }
}
```

#### 3. 并发处理优化器 (src/optimizers/cache-optimizer.ts)
```typescript
import { EventEmitter } from 'events'

export interface CacheEntry<T> {
    value: T
    timestamp: number
    accessCount: number
    lastAccessed: number
    size: number
}

export interface CacheStats {
    hits: number
    misses: number
    evictions: number
    totalSize: number
    entryCount: number
}

export class OptimizedCache<K, V> extends EventEmitter {
    private cache = new Map<K, CacheEntry<V>>()
    private accessOrder = new Map<K, number>() // LRU 跟踪
    private stats: CacheStats = {
        hits: 0,
        misses: 0,
        evictions: 0,
        totalSize: 0,
        entryCount: 0
    }
    private accessCounter = 0

    constructor(
        private maxSize: number = 1000,
        private maxMemory: number = 50 * 1024 * 1024, // 50MB
        private ttl: number = 5 * 60 * 1000, // 5分钟
        private sizeCalculator?: (value: V) => number
    ) {
        super()

        // 定期清理过期条目
        setInterval(() => this.cleanup(), 60000) // 每分钟清理一次
    }

    public set(key: K, value: V): void {
        const now = Date.now()
        const size = this.calculateSize(value)

        // 检查是否需要腾出空间
        this.ensureCapacity(size)

        // 如果键已存在，更新统计信息
        if (this.cache.has(key)) {
            const oldEntry = this.cache.get(key)!
            this.stats.totalSize -= oldEntry.size
        } else {
            this.stats.entryCount++
        }

        // 创建新条目
        const entry: CacheEntry<V> = {
            value,
            timestamp: now,
            accessCount: 1,
            lastAccessed: now,
            size
        }

        this.cache.set(key, entry)
        this.accessOrder.set(key, ++this.accessCounter)
        this.stats.totalSize += size

        this.emit('set', key, value)
    }

    public get(key: K): V | undefined {
        const entry = this.cache.get(key)

        if (!entry) {
            this.stats.misses++
            this.emit('miss', key)
            return undefined
        }

        // 检查是否过期
        if (this.isExpired(entry)) {
            this.delete(key)
            this.stats.misses++
            this.emit('miss', key)
            return undefined
        }

        // 更新访问信息
        entry.accessCount++
        entry.lastAccessed = Date.now()
        this.accessOrder.set(key, ++this.accessCounter)

        this.stats.hits++
        this.emit('hit', key, entry.value)
        return entry.value
    }

    public has(key: K): boolean {
        const entry = this.cache.get(key)
        return entry !== undefined && !this.isExpired(entry)
    }

    public delete(key: K): boolean {
        const entry = this.cache.get(key)
        if (!entry) return false

        this.cache.delete(key)
        this.accessOrder.delete(key)
        this.stats.totalSize -= entry.size
        this.stats.entryCount--

        this.emit('delete', key)
        return true
    }

    public clear(): void {
        this.cache.clear()
        this.accessOrder.clear()
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalSize: 0,
            entryCount: 0
        }
        this.accessCounter = 0
        this.emit('clear')
    }

    // 智能预加载
    public async preload<T>(
        keys: K[],
        loader: (key: K) => Promise<V>,
        concurrency: number = 5
    ): Promise<void> {
        const chunks = this.chunkArray(keys, concurrency)

        for (const chunk of chunks) {
            const promises = chunk.map(async (key) => {
                if (!this.has(key)) {
                    try {
                        const value = await loader(key)
                        this.set(key, value)
                    } catch (error) {
                        this.emit('preloadError', key, error)
                    }
                }
            })

            await Promise.all(promises)
        }
    }

    // 批量操作
    public mget(keys: K[]): Map<K, V> {
        const result = new Map<K, V>()

        keys.forEach(key => {
            const value = this.get(key)
            if (value !== undefined) {
                result.set(key, value)
            }
        })

        return result
    }

    public mset(entries: Map<K, V>): void {
        entries.forEach((value, key) => {
            this.set(key, value)
        })
    }

    private ensureCapacity(newEntrySize: number): void {
        // 检查内存限制
        while (this.stats.totalSize + newEntrySize > this.maxMemory) {
            this.evictLRU()
        }

        // 检查条目数量限制
        while (this.stats.entryCount >= this.maxSize) {
            this.evictLRU()
        }
    }

    private evictLRU(): void {
        if (this.cache.size === 0) return

        // 找到最少最近使用的条目
        let lruKey: K | undefined
        let lruAccessOrder = Infinity

        for (const [key, accessOrder] of this.accessOrder) {
            if (accessOrder < lruAccessOrder) {
                lruAccessOrder = accessOrder
                lruKey = key
            }
        }

        if (lruKey !== undefined) {
            this.delete(lruKey)
            this.stats.evictions++
            this.emit('evict', lruKey)
        }
    }

    private cleanup(): void {
        const now = Date.now()
        const keysToDelete: K[] = []

        for (const [key, entry] of this.cache) {
            if (this.isExpired(entry, now)) {
                keysToDelete.push(key)
            }
        }

        keysToDelete.forEach(key => this.delete(key))

        if (keysToDelete.length > 0) {
            this.emit('cleanup', keysToDelete.length)
        }
    }

    private isExpired(entry: CacheEntry<V>, now: number = Date.now()): boolean {
        return now - entry.timestamp > this.ttl
    }

    private calculateSize(value: V): number {
        if (this.sizeCalculator) {
            return this.sizeCalculator(value)
        }

        // 简单的大小估算
        const str = JSON.stringify(value)
        return str.length * 2 // 假设每个字符占用2字节
    }

    private chunkArray<T>(array: T[], chunkSize: number): T[][] {
        const chunks: T[][] = []
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize))
        }
        return chunks
    }

    public getStats(): CacheStats {
        return { ...this.stats }
    }

    public getHitRate(): number {
        const total = this.stats.hits + this.stats.misses
        return total === 0 ? 0 : this.stats.hits / total
    }

    // 获取热点数据
    public getHotKeys(limit: number = 10): Array<{ key: K; accessCount: number }> {
        return Array.from(this.cache.entries())
            .map(([key, entry]) => ({ key, accessCount: entry.accessCount }))
            .sort((a, b) => b.accessCount - a.accessCount)
            .slice(0, limit)
    }
}
```

### 学习重点

1. **算法复杂度分析**
   - 时间复杂度和空间复杂度的实际测量
   - 代码质量指标的计算
   - AST 解析和代码分析

2. **性能优化技术**
   - 缓存策略的设计和实现
   - 搜索算法的优化
   - 并发处理和资源管理

3. **监控和度量**
   - 性能指标的收集和分析
   - 实时监控系统的设计
   - 报告生成和可视化

### 验收标准

#### 功能验收
- [ ] 能够准确分析代码复杂度
- [ ] 搜索性能明显优于基础实现
- [ ] 缓存系统工作稳定可靠
- [ ] 性能报告详细准确

#### 性能验收
- [ ] 大文件分析时间在可接受范围内
- [ ] 内存使用控制在合理范围
- [ ] 并发处理无死锁和竞态条件
- [ ] 缓存命中率达到预期目标

#### 代码质量验收
- [ ] 算法实现正确高效
- [ ] 错误处理完善
- [ ] 代码结构清晰
- [ ] 测试覆盖率高

### 扩展挑战

1. **机器学习优化**
   ```typescript
   // 使用机器学习预测性能瓶颈
   export class MLPerformancePredictor {
       async predictBottlenecks(codeMetrics: ComplexityMetrics): Promise<string[]> {
           // 实现基于历史数据的性能预测
       }
   }
   ```

2. **分布式缓存**
   ```typescript
   // 实现分布式缓存系统
   export class DistributedCache<K, V> extends OptimizedCache<K, V> {
       // 实现跨节点的缓存同步
   }
   ```

3. **实时性能监控**
   ```typescript
   // 实现实时性能监控仪表板
   export class PerformanceMonitor {
       // 实现实时指标收集和展示
   }
   ```

## 📈 学习进度建议

### 项目完成顺序
1. **项目1 → 项目2** - 建立基础架构和类型安全理解
2. **项目3 → 项目4** - 掌握前端开发和扩展开发技能
3. **项目5** - 综合应用所有学到的技术

### 时间分配建议
- **总时间**: 20-30天（每天2-3小时）
- **项目1**: 3-5天（基础架构）
- **项目2**: 2-3天（类型安全）
- **项目3**: 4-6天（状态管理）
- **项目4**: 5-7天（扩展开发）
- **项目5**: 4-6天（性能优化）
- **总结复习**: 2-3天

### 技能发展路径

```
基础技能 → 进阶技能 → 高级技能 → 专家技能
    ↓         ↓         ↓         ↓
  项目1     项目2     项目3     项目4,5
Monorepo   TypeScript  React    VS Code
设计模式   类型安全    状态管理   扩展开发
                              性能优化
```

---

## 🎯 学习成果验证

### 技能评估标准

#### 初级水平 (项目1-2完成后)
- [ ] 能够理解和搭建 Monorepo 项目
- [ ] 掌握基本的设计模式应用
- [ ] 能够使用 Zod 进行类型验证
- [ ] 理解 TypeScript 高级特性

#### 中级水平 (项目3-4完成后)
- [ ] 能够设计复杂的状态管理系统
- [ ] 掌握 React 性能优化技巧
- [ ] 能够开发完整的 VS Code 扩展
- [ ] 理解 Webview 通信机制

#### 高级水平 (项目5完成后)
- [ ] 能够分析和优化算法性能
- [ ] 掌握并发处理和缓存策略
- [ ] 能够设计性能监控系统
- [ ] 具备系统架构设计能力

### 作品集建议

完成所有项目后，您将拥有：

1. **Mini AI Assistant** - 展示架构设计能力
2. **Type-Safe Config** - 展示类型安全设计
3. **React State Manager** - 展示状态管理技能
4. **VS Code Extension** - 展示扩展开发能力
5. **Performance Optimizer** - 展示性能优化技能

这些项目可以作为您的技术作品集，展示给潜在雇主或开源社区。

---

## 🚀 进阶学习建议

### 完成所有项目后的下一步

1. **开源贡献**
   - 向 Roo Code 项目贡献代码
   - 参与其他开源项目
   - 维护自己的开源项目

2. **技术深化**
   - 学习更多 AI 相关技术
   - 深入研究编译器和语言服务器
   - 探索 WebAssembly 和性能优化

3. **架构设计**
   - 学习微服务架构
   - 掌握分布式系统设计
   - 研究大规模系统架构

4. **团队协作**
   - 学习代码审查最佳实践
   - 掌握项目管理技能
   - 提升技术领导力

### 推荐的后续学习资源

#### 书籍推荐
- 《Clean Architecture》- Robert C. Martin
- 《Designing Data-Intensive Applications》- Martin Kleppmann
- 《System Design Interview》- Alex Xu
- 《TypeScript Programming》- Boris Cherny

#### 在线课程
- [Frontend Masters](https://frontendmasters.com/) - 前端进阶课程
- [Pluralsight](https://www.pluralsight.com/) - 技术技能提升
- [Coursera](https://www.coursera.org/) - 计算机科学基础

#### 技术社区
- [GitHub](https://github.com/) - 开源项目参与
- [Stack Overflow](https://stackoverflow.com/) - 技术问答
- [Dev.to](https://dev.to/) - 技术博客分享
- [Reddit r/programming](https://www.reddit.com/r/programming/) - 技术讨论

---

## 📝 项目模板和工具

### 项目初始化脚本

```bash
#!/bin/bash
# create-practice-project.sh

PROJECT_NAME=$1
PROJECT_TYPE=$2

if [ -z "$PROJECT_NAME" ] || [ -z "$PROJECT_TYPE" ]; then
    echo "Usage: ./create-practice-project.sh <project-name> <project-type>"
    echo "Project types: monorepo, config, state-manager, vscode-extension, optimizer"
    exit 1
fi

mkdir $PROJECT_NAME
cd $PROJECT_NAME

case $PROJECT_TYPE in
    "monorepo")
        # 创建 Monorepo 项目结构
        mkdir -p packages/{core,providers,types} apps/cli
        echo '{"name": "'$PROJECT_NAME'", "private": true}' > package.json
        echo 'packages:\n  - "packages/*"\n  - "apps/*"' > pnpm-workspace.yaml
        ;;
    "config")
        # 创建配置系统项目结构
        mkdir -p src/{schemas,managers,validators} examples tests
        npm init -y
        npm install zod
        ;;
    "state-manager")
        # 创建状态管理项目结构
        mkdir -p src/{core,middleware,utils} examples tests
        npm init -y
        npm install react @types/react
        ;;
    "vscode-extension")
        # 创建 VS Code 扩展项目结构
        mkdir -p src/{extension,webview,shared} media syntaxes
        npm init -y
        npm install vscode @types/vscode
        ;;
    "optimizer")
        # 创建性能优化工具项目结构
        mkdir -p src/{analyzers,optimizers,algorithms,utils} benchmarks examples tests
        npm init -y
        npm install typescript @types/node
        ;;
    *)
        echo "Unknown project type: $PROJECT_TYPE"
        exit 1
        ;;
esac

echo "Project $PROJECT_NAME created successfully!"
```

### 通用配置文件模板

#### TypeScript 配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}
```

#### Jest 配置 (jest.config.js)
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
}
```

---

## 🎉 结语

通过完成这5个渐进式实践项目，您将：

### 获得的核心技能
1. **现代前端架构设计** - Monorepo、微前端、组件化
2. **类型安全编程** - TypeScript 高级特性、运行时验证
3. **状态管理专家** - 复杂应用状态的设计和优化
4. **工具开发能力** - VS Code 扩展、开发工具链
5. **性能优化专家** - 算法优化、系统性能调优

### 职业发展方向
- **高级前端工程师** - 具备架构设计和性能优化能力
- **全栈开发工程师** - 掌握前后端技术栈
- **开发工具工程师** - 专注于开发者体验和工具链
- **技术架构师** - 系统设计和技术决策
- **开源项目维护者** - 社区贡献和技术影响力

### 持续成长建议
1. **保持学习热情** - 技术在快速发展，持续学习是关键
2. **实践驱动学习** - 通过实际项目巩固理论知识
3. **分享和交流** - 写技术博客，参与技术社区
4. **关注行业趋势** - 跟上最新的技术发展和最佳实践
5. **培养软技能** - 沟通、协作、领导力同样重要

**记住**：这些项目只是您技术成长路径的起点。真正的专家是通过不断的实践、思考和创新来塑造的。

祝您在技术成长的道路上取得成功！🚀

---

**最后更新**: 2024年12月
**文档版本**: v1.0
**适用于**: Roo Code 项目学习者