# Roo Code 学习实践项目集

> 基于学习路线图设计的渐进式实践项目，帮助巩固每个阶段的学习成果

## 🎯 项目概览

| 项目名称 | 对应阶段 | 难度等级 | 预计时间 | 核心技能 |
|---------|---------|---------|---------|---------|
| Mini Monorepo | 第1-2阶段 | 🟢 初级 | 3-5天 | Monorepo、设计模式 |
| Type-Safe Config | 第3阶段 | 🟡 中级 | 2-3天 | TypeScript、Zod |
| React State Manager | 第4阶段 | 🟡 中级 | 4-6天 | React、状态管理 |
| VS Code Extension | 第4阶段 | 🔴 高级 | 5-7天 | 扩展开发、Webview |
| Performance Optimizer | 第5阶段 | 🔴 高级 | 4-6天 | 算法、性能优化 |

---

## 🚀 项目1：Mini Monorepo - 简化版 AI 助手

**适用阶段**: 第1-2阶段完成后  
**难度等级**: 🟢 初级  
**预计时间**: 3-5天  

### 项目目标
创建一个基础的 AI 助手框架，重点学习 Monorepo 架构和设计模式的实际应用。

### 技能要求
- [x] 完成第1阶段：项目结构和基础架构理解
- [x] 完成第2阶段：核心架构模式学习
- [ ] 基础的 Node.js 和 TypeScript 知识

### 项目结构
```
mini-ai-assistant/
├── 📁 packages/
│   ├── 📁 core/                 # 核心逻辑包
│   │   ├── 📄 package.json
│   │   ├── 📁 src/
│   │   │   ├── 📄 index.ts
│   │   │   ├── 📄 assistant.ts
│   │   │   └── 📄 types.ts
│   │   └── 📄 tsconfig.json
│   ├── 📁 providers/            # AI 提供商包
│   │   ├── 📄 package.json
│   │   ├── 📁 src/
│   │   │   ├── 📄 index.ts
│   │   │   ├── 📄 base-provider.ts
│   │   │   ├── 📄 mock-provider.ts
│   │   │   └── 📄 openai-provider.ts
│   │   └── 📄 tsconfig.json
│   └── 📁 types/                # 共享类型包
│       ├── 📄 package.json
│       ├── 📁 src/
│       │   ├── 📄 index.ts
│       │   ├── 📄 assistant.ts
│       │   └── 📄 provider.ts
│       └── 📄 tsconfig.json
├── 📁 apps/
│   └── 📁 cli/                  # 命令行应用
│       ├── 📄 package.json
│       ├── 📁 src/
│       │   ├── 📄 index.ts
│       │   └── 📄 commands.ts
│       └── 📄 tsconfig.json
├── 📄 package.json              # 根包配置
├── 📄 pnpm-workspace.yaml       # 工作区配置
├── 📄 turbo.json               # 构建配置
└── 📄 tsconfig.json            # 根 TS 配置
```

### 核心功能实现

#### 1. 工厂模式实现 (packages/providers/src/index.ts)
```typescript
import { BaseProvider } from './base-provider'
import { MockProvider } from './mock-provider'
import { OpenAIProvider } from './openai-provider'
import { ProviderConfig, ProviderType } from '@mini-ai/types'

export function createProvider(config: ProviderConfig): BaseProvider {
    switch (config.type) {
        case ProviderType.MOCK:
            return new MockProvider(config)
        case ProviderType.OPENAI:
            return new OpenAIProvider(config)
        default:
            throw new Error(`Unsupported provider type: ${config.type}`)
    }
}

export * from './base-provider'
export * from './mock-provider'
export * from './openai-provider'
```

#### 2. 抽象基类设计 (packages/providers/src/base-provider.ts)
```typescript
import { Message, Response, ProviderConfig } from '@mini-ai/types'

export abstract class BaseProvider {
    protected config: ProviderConfig

    constructor(config: ProviderConfig) {
        this.config = config
    }

    abstract async sendMessage(message: Message): Promise<Response>
    
    abstract async validateConfig(): Promise<boolean>

    protected formatMessage(message: Message): string {
        return `${message.role}: ${message.content}`
    }

    protected createResponse(content: string, metadata?: any): Response {
        return {
            content,
            metadata: {
                provider: this.config.type,
                timestamp: Date.now(),
                ...metadata
            }
        }
    }
}
```

#### 3. 具体实现示例 (packages/providers/src/mock-provider.ts)
```typescript
import { BaseProvider } from './base-provider'
import { Message, Response } from '@mini-ai/types'

export class MockProvider extends BaseProvider {
    async sendMessage(message: Message): Promise<Response> {
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const responses = [
            "这是一个模拟回复。",
            "我理解您的问题。",
            "让我为您提供帮助。",
            "这是一个很好的问题。"
        ]
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)]
        
        return this.createResponse(randomResponse, {
            model: 'mock-model-v1',
            tokens: Math.floor(Math.random() * 100) + 50
        })
    }

    async validateConfig(): Promise<boolean> {
        return true // Mock provider 总是有效的
    }
}
```

### 学习重点

1. **Monorepo 架构实践**
   - 理解包之间的依赖关系
   - 掌握 pnpm workspace 的使用
   - 学习 Turbo 构建优化

2. **设计模式应用**
   - 工厂模式的实际实现
   - 抽象基类的设计原则
   - 策略模式的变体应用

3. **TypeScript 项目组织**
   - 类型定义的共享和复用
   - 模块导入导出的最佳实践
   - 编译配置的继承关系

### 验收标准

#### 功能验收
- [ ] 能够成功创建不同类型的 AI 提供商
- [ ] 命令行应用能够正常工作
- [ ] 所有包都能正确构建和测试
- [ ] 类型检查无错误

#### 代码质量验收
- [ ] 遵循 SOLID 原则
- [ ] 具有良好的错误处理
- [ ] 包含必要的单元测试
- [ ] 代码注释清晰完整

#### 架构验收
- [ ] 包依赖关系清晰合理
- [ ] 抽象层次设计恰当
- [ ] 易于扩展新的提供商
- [ ] 配置管理灵活可靠

### 扩展挑战

1. **添加新的提供商**
   ```typescript
   // 实现一个 Anthropic 提供商
   export class AnthropicProvider extends BaseProvider {
       // 实现具体逻辑
   }
   ```

2. **实现配置验证**
   ```typescript
   // 使用 Zod 进行配置验证
   const configSchema = z.object({
       type: z.enum(['mock', 'openai', 'anthropic']),
       apiKey: z.string().optional(),
       model: z.string().optional()
   })
   ```

3. **添加缓存机制**
   ```typescript
   // 实现简单的内存缓存
   export class CachedProvider extends BaseProvider {
       private cache = new Map<string, Response>()
       // 实现缓存逻辑
   }
   ```

### 学习资源

- [pnpm Workspaces 文档](https://pnpm.io/workspaces)
- [Turborepo 入门指南](https://turbo.build/repo/docs/getting-started)
- [TypeScript Project References](https://www.typescriptlang.org/docs/handbook/project-references.html)
- [设计模式 TypeScript 实现](https://refactoring.guru/design-patterns/typescript)

---

## 🔧 项目2：Type-Safe Config - 类型安全的配置系统

**适用阶段**: 第3阶段完成后  
**难度等级**: 🟡 中级  
**预计时间**: 2-3天  

### 项目目标
实现一个完整的配置管理系统，重点学习 Zod + TypeScript 的类型安全设计。

### 技能要求
- [x] 完成第3阶段：TypeScript 类型安全设计
- [x] 理解 Zod schema 的基本使用
- [ ] 熟悉文件系统操作

### 项目结构
```
type-safe-config/
├── 📁 src/
│   ├── 📄 index.ts              # 主入口
│   ├── 📄 config-manager.ts     # 配置管理器
│   ├── 📄 schemas.ts            # Zod schemas
│   ├── 📄 types.ts              # TypeScript 类型
│   ├── 📄 validators.ts         # 自定义验证器
│   └── 📄 utils.ts              # 工具函数
├── 📁 examples/
│   ├── 📄 basic-usage.ts        # 基础使用示例
│   ├── 📄 advanced-usage.ts     # 高级使用示例
│   └── 📄 config-files/         # 示例配置文件
├── 📁 tests/
│   ├── 📄 config-manager.test.ts
│   ├── 📄 schemas.test.ts
│   └── 📄 validators.test.ts
├── 📄 package.json
├── 📄 tsconfig.json
└── 📄 README.md
```

### 核心功能实现

#### 1. Zod Schema 设计 (src/schemas.ts)
```typescript
import { z } from 'zod'

// 基础配置 schema
export const baseConfigSchema = z.object({
    name: z.string().min(1, "配置名称不能为空"),
    version: z.string().regex(/^\d+\.\d+\.\d+$/, "版本格式必须为 x.y.z"),
    description: z.string().optional(),
    enabled: z.boolean().default(true),
})

// 数据库配置 schema
export const databaseConfigSchema = z.object({
    host: z.string().min(1),
    port: z.number().int().min(1).max(65535),
    database: z.string().min(1),
    username: z.string().min(1),
    password: z.string().min(1),
    ssl: z.boolean().default(false),
    connectionTimeout: z.number().int().positive().default(5000),
})

// API 配置 schema
export const apiConfigSchema = z.object({
    baseUrl: z.string().url("必须是有效的 URL"),
    apiKey: z.string().min(1),
    timeout: z.number().int().positive().default(30000),
    retries: z.number().int().min(0).max(5).default(3),
    rateLimit: z.object({
        requests: z.number().int().positive(),
        window: z.number().int().positive(), // 时间窗口（毫秒）
    }).optional(),
})

// 完整应用配置 schema
export const appConfigSchema = z.object({
    ...baseConfigSchema.shape,
    database: databaseConfigSchema,
    api: apiConfigSchema,
    features: z.record(z.string(), z.boolean()).default({}),
    logging: z.object({
        level: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
        file: z.string().optional(),
        console: z.boolean().default(true),
    }).default({}),
})

// 类型推导
export type BaseConfig = z.infer<typeof baseConfigSchema>
export type DatabaseConfig = z.infer<typeof databaseConfigSchema>
export type ApiConfig = z.infer<typeof apiConfigSchema>
export type AppConfig = z.infer<typeof appConfigSchema>
```

#### 2. 泛型配置管理器 (src/config-manager.ts)
```typescript
import { z } from 'zod'
import * as fs from 'fs/promises'
import * as path from 'path'

export class ConfigManager<T extends z.ZodType> {
    private schema: T
    private configPath: string
    private config: z.infer<T> | null = null
    private watchers: Array<(config: z.infer<T>) => void> = []

    constructor(schema: T, configPath: string) {
        this.schema = schema
        this.configPath = path.resolve(configPath)
    }

    // 加载配置
    async load(): Promise<z.infer<T>> {
        try {
            const configData = await fs.readFile(this.configPath, 'utf-8')
            const rawConfig = JSON.parse(configData)
            
            // 使用 Zod 验证和转换
            const validatedConfig = this.schema.parse(rawConfig)
            this.config = validatedConfig
            
            // 通知观察者
            this.notifyWatchers(validatedConfig)
            
            return validatedConfig
        } catch (error) {
            if (error instanceof z.ZodError) {
                throw new ConfigValidationError('配置验证失败', error.errors)
            }
            throw new ConfigLoadError(`无法加载配置文件: ${error.message}`)
        }
    }

    // 保存配置
    async save(config: z.infer<T>): Promise<void> {
        try {
            // 验证配置
            const validatedConfig = this.schema.parse(config)
            
            // 确保目录存在
            await fs.mkdir(path.dirname(this.configPath), { recursive: true })
            
            // 保存配置
            await fs.writeFile(
                this.configPath, 
                JSON.stringify(validatedConfig, null, 2),
                'utf-8'
            )
            
            this.config = validatedConfig
            this.notifyWatchers(validatedConfig)
        } catch (error) {
            if (error instanceof z.ZodError) {
                throw new ConfigValidationError('配置验证失败', error.errors)
            }
            throw new ConfigSaveError(`无法保存配置文件: ${error.message}`)
        }
    }

    // 获取当前配置
    get(): z.infer<T> | null {
        return this.config
    }

    // 部分更新配置
    async update(partialConfig: Partial<z.infer<T>>): Promise<z.infer<T>> {
        if (!this.config) {
            throw new Error('配置尚未加载，请先调用 load()')
        }

        const updatedConfig = { ...this.config, ...partialConfig }
        await this.save(updatedConfig)
        return updatedConfig
    }

    // 验证配置（不保存）
    validate(config: unknown): z.SafeParseResult<z.infer<T>> {
        return this.schema.safeParse(config)
    }

    // 监听配置变化
    watch(callback: (config: z.infer<T>) => void): () => void {
        this.watchers.push(callback)
        
        // 返回取消监听的函数
        return () => {
            const index = this.watchers.indexOf(callback)
            if (index > -1) {
                this.watchers.splice(index, 1)
            }
        }
    }

    private notifyWatchers(config: z.infer<T>): void {
        this.watchers.forEach(callback => {
            try {
                callback(config)
            } catch (error) {
                console.error('配置监听器执行失败:', error)
            }
        })
    }
}

// 自定义错误类
export class ConfigValidationError extends Error {
    constructor(message: string, public errors: z.ZodIssue[]) {
        super(message)
        this.name = 'ConfigValidationError'
    }
}

export class ConfigLoadError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'ConfigLoadError'
    }
}

export class ConfigSaveError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'ConfigSaveError'
    }
}
```

### 学习重点

1. **Zod Schema 设计**
   - 复杂对象的 schema 定义
   - 自定义验证规则
   - 默认值和可选字段处理

2. **泛型约束应用**
   - 泛型类的设计
   - 类型推导和类型安全
   - 条件类型的使用

3. **运行时类型验证**
   - 错误处理和用户友好的错误信息
   - 性能优化考虑
   - 验证结果的处理

### 验收标准

#### 功能验收
- [ ] 能够加载和保存 JSON 配置文件
- [ ] 配置验证错误信息清晰易懂
- [ ] 支持配置的部分更新
- [ ] 配置变化监听机制正常工作

#### 类型安全验收
- [ ] 所有配置操作都是类型安全的
- [ ] 编译时能够捕获类型错误
- [ ] 运行时验证与编译时类型一致
- [ ] 自动补全和类型提示完整

### 扩展挑战

1. **环境变量集成**
   ```typescript
   // 支持从环境变量覆盖配置
   const envOverrideSchema = z.object({
       DATABASE_HOST: z.string().optional(),
       API_KEY: z.string().optional(),
   })
   ```

2. **配置热重载**
   ```typescript
   // 监听文件变化并自动重新加载
   import chokidar from 'chokidar'
   
   class HotReloadConfigManager extends ConfigManager {
       // 实现文件监听逻辑
   }
   ```

3. **配置加密**
   ```typescript
   // 敏感配置的加密存储
   const encryptedConfigSchema = z.object({
       encrypted: z.string(),
       salt: z.string(),
   })
   ```

---

## ⚛️ 项目3：React State Manager - 复杂状态管理系统

**适用阶段**: 第4阶段完成后  
**难度等级**: 🟡 中级  
**预计时间**: 4-6天  

### 项目目标
实现一个类似 Redux 但更简单的状态管理库，学习 React Context、Reducer 模式和性能优化。

### 技能要求
- [x] 完成第4阶段：前端组件和状态管理
- [x] 熟悉 React Hooks 和 Context API
- [ ] 理解 Reducer 模式

### 项目结构
```
react-state-manager/
├── 📁 src/
│   ├── 📁 core/
│   │   ├── 📄 store.ts          # 核心 Store 实现
│   │   ├── 📄 context.ts        # React Context 封装
│   │   ├── 📄 hooks.ts          # 自定义 Hooks
│   │   └── 📄 types.ts          # 类型定义
│   ├── 📁 middleware/
│   │   ├── 📄 logger.ts         # 日志中间件
│   │   ├── 📄 persist.ts        # 持久化中间件
│   │   └── 📄 devtools.ts       # 开发工具中间件
│   ├── 📁 utils/
│   │   ├── 📄 immutable.ts      # 不可变更新工具
│   │   └── 📄 selector.ts       # 选择器工具
│   └── 📄 index.ts              # 主入口
├── 📁 examples/
│   ├── 📁 todo-app/             # Todo 应用示例
│   ├── 📁 counter-app/          # 计数器示例
│   └── 📁 async-app/            # 异步操作示例
├── 📁 tests/
└── 📄 package.json
```

### 核心功能实现

#### 1. 核心 Store 实现 (src/core/store.ts)
```typescript
import { Reducer, Dispatch } from 'react'

export interface Action<T = any> {
    type: string
    payload?: T
}

export interface Middleware<S = any> {
    (store: Store<S>): (next: Dispatch<Action>) => (action: Action) => void
}

export interface StoreConfig<S> {
    reducer: Reducer<S, Action>
    initialState: S
    middleware?: Middleware<S>[]
    enhancers?: StoreEnhancer<S>[]
}

export interface StoreEnhancer<S> {
    (createStore: StoreCreator): StoreCreator
}

export interface StoreCreator {
    <S>(config: StoreConfig<S>): Store<S>
}

export interface Store<S = any> {
    getState(): S
    dispatch(action: Action): void
    subscribe(listener: () => void): () => void
    replaceReducer(nextReducer: Reducer<S, Action>): void
}

export class SimpleStore<S> implements Store<S> {
    private state: S
    private reducer: Reducer<S, Action>
    private listeners: Set<() => void> = new Set()
    private middleware: Middleware<S>[]
    private dispatch: Dispatch<Action>

    constructor(config: StoreConfig<S>) {
        this.state = config.initialState
        this.reducer = config.reducer
        this.middleware = config.middleware || []
        
        // 构建中间件链
        this.dispatch = this.applyMiddleware()
    }

    getState(): S {
        return this.state
    }

    dispatch(action: Action): void {
        // 这个方法会被中间件链替换
        this.state = this.reducer(this.state, action)
        this.notifyListeners()
    }

    subscribe(listener: () => void): () => void {
        this.listeners.add(listener)
        
        return () => {
            this.listeners.delete(listener)
        }
    }

    replaceReducer(nextReducer: Reducer<S, Action>): void {
        this.reducer = nextReducer
        this.dispatch({ type: '@@REPLACE_REDUCER' })
    }

    private applyMiddleware(): Dispatch<Action> {
        if (this.middleware.length === 0) {
            return this.dispatch.bind(this)
        }

        const middlewareAPI = {
            getState: this.getState.bind(this),
            dispatch: (action: Action) => this.dispatch(action)
        }

        const chain = this.middleware.map(middleware => middleware(middlewareAPI))
        
        return chain.reduce(
            (composed, middleware) => middleware(composed),
            this.baseDispatch.bind(this)
        )
    }

    private baseDispatch(action: Action): void {
        this.state = this.reducer(this.state, action)
        this.notifyListeners()
    }

    private notifyListeners(): void {
        this.listeners.forEach(listener => {
            try {
                listener()
            } catch (error) {
                console.error('Store listener error:', error)
            }
        })
    }
}

export function createStore<S>(config: StoreConfig<S>): Store<S> {
    return new SimpleStore(config)
}
```

#### 2. React Context 封装 (src/core/context.ts)
```typescript
import React, { createContext, useContext, useReducer, ReactNode, Reducer } from 'react'
import { Action, Store, createStore, StoreConfig } from './store'

interface StoreContextValue<S> {
    store: Store<S>
}

export function createStoreContext<S>() {
    const StoreContext = createContext<StoreContextValue<S> | null>(null)

    interface StoreProviderProps {
        config: StoreConfig<S>
        children: ReactNode
    }

    function StoreProvider({ config, children }: StoreProviderProps) {
        const [store] = React.useState(() => createStore(config))

        return (
            <StoreContext.Provider value={{ store }}>
                {children}
            </StoreContext.Provider>
        )
    }

    function useStore(): Store<S> {
        const context = useContext(StoreContext)
        if (!context) {
            throw new Error('useStore must be used within a StoreProvider')
        }
        return context.store
    }

    return {
        StoreProvider,
        useStore,
        StoreContext
    }
}

// 通用的 Provider 组件
export interface ProviderProps<S> {
    reducer: Reducer<S, Action>
    initialState: S
    children: ReactNode
}

export function createProvider<S>() {
    const { StoreProvider, useStore } = createStoreContext<S>()

    function Provider({ reducer, initialState, children }: ProviderProps<S>) {
        return (
            <StoreProvider config={{ reducer, initialState }}>
                {children}
            </StoreProvider>
        )
    }

    return {
        Provider,
        useStore
    }
}
```

### 学习重点

1. **React Context 模式**
   - Context 的创建和使用
   - Provider 组件的设计
   - Context 性能优化

2. **Reducer 模式**
   - 状态更新的不可变性
   - Action 的设计原则
   - 复杂状态的管理

3. **自定义 Hooks**
   - Hook 的组合和复用
   - 性能优化技巧
   - 错误处理

### 验收标准

#### 功能验收
- [ ] 状态管理功能完整可用
- [ ] 支持中间件扩展
- [ ] 组件能够正确订阅状态变化
- [ ] 性能表现良好

#### 代码质量验收
- [ ] 类型安全完整
- [ ] 错误处理完善
- [ ] 代码结构清晰
- [ ] 测试覆盖率高

---

*由于内容较长，我将分部分继续编辑文档...*
