# Roo Code 项目快速开始指南

> 基于学习路线图的快速上手指南，帮助您快速搭建开发环境并开始学习

## 🚀 环境准备

### 系统要求
- **Node.js**: >= 20.18.1
- **pnpm**: >= 10.8.1 (推荐使用 pnpm 而非 npm)
- **VS Code**: 最新版本
- **Git**: 用于版本控制

### 安装步骤

1. **安装 Node.js**
   ```bash
   # 使用 nvm 安装 (推荐)
   nvm install 20.18.1
   nvm use 20.18.1
   
   # 或直接从官网下载安装
   # https://nodejs.org/
   ```

2. **安装 pnpm**
   ```bash
   npm install -g pnpm@10.8.1
   
   # 验证安装
   pnpm --version
   ```

3. **克隆项目**
   ```bash
   git clone https://github.com/RooVetGit/Roo-Code.git
   cd Roo-Code
   ```

4. **安装依赖**
   ```bash
   # 安装所有依赖 (可能需要几分钟)
   pnpm install
   
   # 构建项目
   pnpm run build
   ```

5. **启动开发环境**
   ```bash
   # 启动 webview 开发服务器
   cd webview-ui
   pnpm run dev
   
   # 在另一个终端窗口，启动扩展调试
   # 在 VS Code 中按 F5 或使用调试面板
   ```

## 📁 项目结构快速导览

```
Roo-Code/
├── 📁 src/                    # 主扩展代码
│   ├── 📁 api/               # AI 提供商和 API 处理
│   ├── 📁 core/              # 核心功能模块
│   ├── 📁 services/          # 服务层代码
│   ├── 📁 shared/            # 共享工具和类型
│   └── 📄 extension.ts       # 扩展入口文件
├── 📁 webview-ui/            # React 前端界面
│   ├── 📁 src/
│   │   ├── 📁 components/    # React 组件
│   │   ├── 📁 context/       # 状态管理
│   │   └── 📁 utils/         # 工具函数
│   └── 📄 package.json
├── 📁 packages/              # 共享包
│   ├── 📁 types/            # TypeScript 类型定义
│   ├── 📁 build/            # 构建工具
│   └── 📁 config-*/         # 配置包
├── 📁 evals/                 # 评估系统
└── 📄 turbo.json            # Turbo 构建配置
```

## 🎯 第一周学习计划

### Day 1-2: 环境搭建和项目熟悉
- [ ] 完成环境搭建
- [ ] 成功启动项目
- [ ] 浏览项目目录结构
- [ ] 阅读 README.md 和相关文档

**重点文件**：
- `package.json` - 了解项目依赖
- `turbo.json` - 理解构建配置
- `src/extension.ts` - 扩展入口点

### Day 3-4: Monorepo 架构理解
- [ ] 分析各个子包的作用
- [ ] 理解包之间的依赖关系
- [ ] 学习 Turbo 构建系统

**重点文件**：
- `pnpm-workspace.yaml`
- `packages/*/package.json`
- `webview-ui/package.json`

### Day 5-7: 核心架构模式
- [ ] 学习工厂模式实现
- [ ] 理解策略模式应用
- [ ] 分析继承层次设计

**重点文件**：
- `src/api/index.ts`
- `src/api/providers/base-provider.ts`
- `src/api/transform/cache-strategy/`

## 🛠️ 开发工具配置

### VS Code 扩展推荐
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "ms-vscode.test-adapter-converter"
  ]
}
```

### 调试配置
在 `.vscode/launch.json` 中已配置好调试环境：
```json
{
  "name": "Run Extension",
  "type": "extensionHost",
  "request": "launch",
  "args": ["--extensionDevelopmentPath=${workspaceFolder}"]
}
```

### 有用的 VS Code 快捷键
- `F5` - 启动扩展调试
- `Ctrl+Shift+P` - 命令面板
- `Ctrl+Shift+I` - 开发者工具
- `Ctrl+R` - 重新加载扩展窗口

## 🧪 测试和验证

### 运行测试
```bash
# 运行所有测试
pnpm test

# 运行特定测试文件
pnpm test -- src/core/tools/__tests__/readFileTool.test.ts

# 运行测试并监听变化
pnpm test -- --watch
```

### 类型检查
```bash
# 检查 TypeScript 类型错误
pnpm run type-check

# 或者使用 tsc 直接检查
npx tsc --noEmit
```

### 代码质量检查
```bash
# ESLint 检查
pnpm run lint

# Prettier 格式化
pnpm run format

# 修复可自动修复的问题
pnpm run lint:fix
```

## 📚 学习资源快速链接

### 官方文档
- [VS Code Extension API](https://code.visualstudio.com/api) - 扩展开发官方指南
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - TypeScript 官方文档
- [React Documentation](https://react.dev/) - React 官方文档
- [Turborepo Docs](https://turbo.build/repo/docs) - Turbo 构建系统文档

### 在线工具
- [TypeScript Playground](https://www.typescriptlang.org/play) - 在线 TS 代码测试
- [Zod Playground](https://zod.dev/) - Zod schema 测试
- [React DevTools](https://react.dev/learn/react-developer-tools) - React 调试工具

## 🔧 常见问题解决

### 安装问题

**问题**: `pnpm install` 失败
```bash
# 解决方案1: 清理缓存
pnpm store prune
rm -rf node_modules
pnpm install

# 解决方案2: 使用 npm 作为备选
npm install
```

**问题**: Node.js 版本不兼容
```bash
# 使用 nvm 切换版本
nvm install 20.18.1
nvm use 20.18.1
```

### 构建问题

**问题**: TypeScript 编译错误
```bash
# 检查类型错误
npx tsc --noEmit

# 查看详细错误信息
pnpm run build --verbose
```

**问题**: Turbo 构建失败
```bash
# 清理构建缓存
pnpm turbo clean
pnpm run build
```

### 调试问题

**问题**: 扩展无法启动
1. 检查 VS Code 版本是否最新
2. 确认所有依赖已正确安装
3. 查看 VS Code 开发者控制台的错误信息

**问题**: Webview 无法加载
1. 确认 webview-ui 开发服务器正在运行
2. 检查端口是否被占用
3. 查看浏览器控制台的错误信息

## 📝 学习笔记建议

### 创建学习笔记目录
```bash
mkdir learning-notes
cd learning-notes

# 为每个学习阶段创建文件
touch stage1-monorepo.md
touch stage2-architecture.md
touch stage3-typescript.md
touch stage4-frontend.md
touch stage5-algorithms.md
```

### 笔记模板
```markdown
# 学习阶段X - 主题

## 日期: YYYY-MM-DD

## 今日学习目标
- [ ] 目标1
- [ ] 目标2

## 重要概念
### 概念1
- 定义：
- 应用场景：
- 代码示例：

## 代码分析
```typescript
// 关键代码片段
// 添加详细注释
```

## 疑问记录
1. 问题描述
   - 尝试的解决方案
   - 最终解决方案

## 今日总结
- 学到的关键知识点
- 完成的实践练习
- 明天的学习计划
```

## 🎯 下一步行动

1. **完成环境搭建** - 确保项目能够正常运行
2. **开始第一阶段学习** - 按照学习路线图进行
3. **建立学习习惯** - 每天固定时间学习 2-3 小时
4. **记录学习过程** - 使用笔记模板记录学习心得
5. **实践验证** - 完成每个阶段的练习项目

**记住**：学习是一个渐进的过程，不要急于求成。遇到问题时，先查阅文档，再寻求帮助。

祝您学习顺利！🚀
