/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: all 0.3s ease;
}

/* 布局容器 */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 260px;
    height: 100vh;
    background: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    transition: transform 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar.collapsed {
    transform: translateX(-260px);
}

.main-content {
    margin-left: 260px;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 0;
}

/* 侧边栏样式 */
.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.logo i {
    font-size: 1.5rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.sidebar-toggle:hover {
    background-color: var(--hover-bg);
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.menu-item:hover {
    background-color: var(--hover-bg);
    color: var(--text-color);
}

.menu-item.active {
    background-color: var(--primary-color);
    color: white;
    border-right: 3px solid var(--primary-dark);
}

.menu-item i {
    font-size: 1.125rem;
    width: 1.25rem;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.5rem;
}

.theme-btn, .focus-btn {
    flex: 1;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-btn:hover, .focus-btn:hover {
    background-color: var(--hover-bg);
}

/* 顶部导航栏 */
.top-header {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-name {
    font-weight: 500;
    color: var(--text-color);
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* 页面容器 */
.page-container {
    padding: 2rem;
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* 卡片样式 */
.card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-hover);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    flex: 1;
}

.card-header i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.card-body {
    color: var(--text-secondary);
}

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* 进度圆环 */
.progress-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--border-color) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    position: relative;
}

.progress-circle::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: var(--card-bg);
    border-radius: 50%;
}

.progress-value {
    position: relative;
    z-index: 1;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.stat {
    text-align: center;
    flex: 1;
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

/* 任务列表 */
.task-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.task-item:hover {
    background: var(--hover-bg);
}

.task-item.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

.task-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 0.25rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.task-checkbox.checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.task-text {
    flex: 1;
    font-size: 0.875rem;
    color: var(--text-color);
}

.task-summary {
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* 统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
}

.stat-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-light);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

.stat-info {
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 200px;
    width: 100%;
}

/* 阶段进度列表 */
.stage-list {
    space-y: 1rem;
}

.stage-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.stage-info {
    flex: 1;
}

.stage-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.stage-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stage-progress-bar {
    width: 100px;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-left: 1rem;
}

.stage-progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--hover-bg);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: var(--danger-dark);
}

/* 表单样式 */
.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background: var(--card-bg);
    color: var(--text-color);
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 0.75rem;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background: var(--hover-bg);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* 学习计划页面样式 */
.learning-plan-container {
    max-width: 1200px;
    margin: 0 auto;
}

.plan-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.plan-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
}

.plan-controls {
    display: flex;
    gap: 1rem;
}

.stages-container {
    display: grid;
    gap: 2rem;
}

.stage-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.stage-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.stage-number {
    width: 3rem;
    height: 3rem;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.25rem;
}

.stage-meta {
    flex: 1;
    margin-left: 1rem;
}

.stage-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.stage-duration {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.stage-progress-indicator {
    text-align: right;
}

.progress-percentage {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.progress-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stage-tasks {
    margin-top: 1.5rem;
}

.stage-task-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.stage-task-item:hover {
    background: var(--hover-bg);
}

.stage-task-item.completed {
    opacity: 0.7;
}

.task-status {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-status.completed {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.task-content {
    flex: 1;
}

.task-title {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.task-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.task-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* 进度跟踪页面样式 */
.progress-tracking-container {
    max-width: 1200px;
    margin: 0 auto;
}

.tracking-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.tracking-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
}

.tracking-controls {
    display: flex;
    gap: 1rem;
}

.tracking-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.time-tracking {
    background: var(--card-bg);
}

.progress-charts {
    background: var(--card-bg);
}

.time-input-form {
    display: grid;
    gap: 1rem;
}

.time-records-table {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.time-records-table h3 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.table-container {
    overflow-x: auto;
}

.records-table {
    width: 100%;
    border-collapse: collapse;
}

.records-table th,
.records-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.records-table th {
    background: var(--bg-color);
    font-weight: 600;
    color: var(--text-color);
}

.records-table td {
    color: var(--text-secondary);
}

/* 文档查看器样式 */
.document-viewer-container {
    max-width: 1200px;
    margin: 0 auto;
}

.viewer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.viewer-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
}

.viewer-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.document-content {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    min-height: 500px;
    box-shadow: var(--shadow);
}

.document-content h1,
.document-content h2,
.document-content h3,
.document-content h4,
.document-content h5,
.document-content h6 {
    color: var(--text-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.document-content h1 {
    font-size: 2rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.document-content h2 {
    font-size: 1.5rem;
}

.document-content h3 {
    font-size: 1.25rem;
}

.document-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

.document-content ul,
.document-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.document-content li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.document-content code {
    background: var(--bg-color);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: var(--primary-color);
}

.document-content pre {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    margin-bottom: 1rem;
}

.document-content pre code {
    background: none;
    padding: 0;
    color: var(--text-color);
}

.document-content blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin: 1rem 0;
    color: var(--text-secondary);
    font-style: italic;
}

.document-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.document-content th,
.document-content td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.document-content th {
    background: var(--bg-color);
    font-weight: 600;
    color: var(--text-color);
}

.document-content td {
    color: var(--text-secondary);
}

/* 实践项目页面样式 */
.projects-container {
    max-width: 1200px;
    margin: 0 auto;
}

.projects-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.projects-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
}

.projects-controls {
    display: flex;
    gap: 1rem;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.project-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.project-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.project-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-light);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.project-meta {
    flex: 1;
    margin-left: 1rem;
}

.project-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.project-difficulty {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.project-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.project-status.not-started {
    background: var(--gray-light);
    color: var(--gray-dark);
}

.project-status.in-progress {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.project-status.completed {
    background: var(--success-light);
    color: var(--success-dark);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-progress {
    margin-bottom: 1.5rem;
}

.project-progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.project-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.project-progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.project-actions {
    display: flex;
    gap: 0.75rem;
}

.project-actions .btn {
    flex: 1;
    justify-content: center;
}

/* 设置页面样式 */
.settings-container {
    max-width: 800px;
    margin: 0 auto;
}

.settings-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
}

.settings-sections {
    display: grid;
    gap: 2rem;
}

.settings-section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.settings-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

.settings-group {
    display: grid;
    gap: 1rem;
}

.setting-item {
    display: grid;
    gap: 0.5rem;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-color);
}

.checkbox-group {
    display: grid;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: var(--primary-color);
}

.about-info {
    color: var(--text-secondary);
    line-height: 1.6;
}

.about-info p {
    margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-260px);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .page-container {
        padding: 1rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-areas:
            "overall"
            "today"
            "stats"
            "radar"
            "trend"
            "stage";
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .top-header {
        padding: 1rem;
    }

    .plan-header,
    .tracking-header,
    .viewer-header,
    .projects-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .plan-controls,
    .tracking-controls,
    .viewer-controls,
    .projects-controls {
        width: 100%;
        justify-content: flex-start;
    }

    .tracking-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

/* 专注模式 */
.focus-mode .sidebar {
    transform: translateX(-260px);
}

.focus-mode .main-content {
    margin-left: 0;
}

.focus-mode .top-header {
    display: none;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.125rem;
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-color);
    color: var(--card-bg);
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
}

/* 文档查看器特殊样式 */
.table-of-contents {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.table-of-contents h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.toc-list {
    list-style: none;
    padding: 0;
}

.toc-item {
    margin-bottom: 0.5rem;
}

.toc-item a {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.25rem 0;
    display: block;
    transition: color 0.2s ease;
}

.toc-item a:hover {
    color: var(--primary-color);
}

.toc-h1 { margin-left: 0; }
.toc-h2 { margin-left: 1rem; }
.toc-h3 { margin-left: 2rem; }
.toc-h4 { margin-left: 3rem; }

.code-copy-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    opacity: 0;
}

.code-copy-btn:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

pre:hover .code-copy-btn {
    opacity: 1;
}
