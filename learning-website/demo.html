<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点击式学习功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .demo-card .icon {
            font-size: 2rem;
        }
        
        .demo-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .demo-card ul {
            list-style: none;
            padding: 0;
        }
        
        .demo-card li {
            margin-bottom: 0.75rem;
            padding-left: 1.5rem;
            position: relative;
        }
        
        .demo-card li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
        
        .cta-section {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .cta-section h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .cta-section p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background: rgba(59, 130, 246, 0.8);
            border-color: rgba(59, 130, 246, 0.8);
        }
        
        .btn-primary:hover {
            background: rgba(59, 130, 246, 1);
        }
        
        .steps {
            margin-top: 3rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .steps h3 {
            text-align: center;
            margin-bottom: 2rem;
            font-size: 1.5rem;
        }
        
        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .step {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 3rem;
            height: 3rem;
            background: rgba(59, 130, 246, 0.8);
            border-radius: 50%;
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .step h4 {
            margin-bottom: 0.5rem;
        }
        
        .step p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
                align-items: center;
            }
            
            .steps-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 点击式学习功能演示</h1>
            <p>体验全新的交互式学习方式，深入理解 Roo Code 项目架构</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card">
                <h3>
                    <span class="icon">🚀</span>
                    一键启动学习
                </h3>
                <p>在学习计划页面，只需点击任务上的"开始学习"按钮，即可启动深度学习模式。</p>
                <ul>
                    <li>自动切换到学习界面</li>
                    <li>加载所有相关文件</li>
                    <li>显示学习重点标注</li>
                    <li>提供详细学习指南</li>
                </ul>
            </div>
            
            <div class="demo-card">
                <h3>
                    <span class="icon">📁</span>
                    智能文件展示
                </h3>
                <p>系统自动展示每个学习任务相关的所有源代码文件和配置文件。</p>
                <ul>
                    <li>源代码文件自动加载</li>
                    <li>语法高亮显示</li>
                    <li>文件间快速切换</li>
                    <li>一键复制代码内容</li>
                </ul>
            </div>
            
            <div class="demo-card">
                <h3>
                    <span class="icon">💡</span>
                    学习重点标注
                </h3>
                <p>每个文件都配有详细的学习重点和关键知识点标注，帮助您快速掌握核心概念。</p>
                <ul>
                    <li>关键代码行标注</li>
                    <li>设计模式解释</li>
                    <li>最佳实践指导</li>
                    <li>常见误区提醒</li>
                </ul>
            </div>
            
            <div class="demo-card">
                <h3>
                    <span class="icon">📖</span>
                    集成学习指南
                </h3>
                <p>每个任务都包含完整的学习指南，从理论到实践，全方位指导您的学习过程。</p>
                <ul>
                    <li>学习目标明确</li>
                    <li>核心概念解释</li>
                    <li>实践练习建议</li>
                    <li>扩展学习资源</li>
                </ul>
            </div>
            
            <div class="demo-card">
                <h3>
                    <span class="icon">📊</span>
                    进度自动跟踪
                </h3>
                <p>学习进度自动记录和同步，技能等级实时更新，让您清楚了解学习成果。</p>
                <ul>
                    <li>任务完成状态同步</li>
                    <li>技能等级自动提升</li>
                    <li>学习时间统计</li>
                    <li>成就系统激励</li>
                </ul>
            </div>
            
            <div class="demo-card">
                <h3>
                    <span class="icon">🎨</span>
                    优雅的用户界面
                </h3>
                <p>现代化的界面设计，响应式布局，支持深色/浅色主题，提供最佳的学习体验。</p>
                <ul>
                    <li>响应式设计</li>
                    <li>主题切换功能</li>
                    <li>专注模式支持</li>
                    <li>移动端适配</li>
                </ul>
            </div>
        </div>
        
        <div class="cta-section">
            <h2>🌟 立即体验新功能</h2>
            <p>开始您的 Roo Code 深度学习之旅，掌握现代前端开发的核心技能</p>
            
            <div class="btn-group">
                <a href="index.html" class="btn btn-primary">
                    🎯 开始学习
                </a>
                <a href="ENHANCED_FEATURES.md" class="btn">
                    📖 功能详解
                </a>
                <a href="README.md" class="btn">
                    📋 使用指南
                </a>
            </div>
        </div>
        
        <div class="steps">
            <h3>🚀 快速上手步骤</h3>
            <div class="steps-grid">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>进入学习计划</h4>
                    <p>点击侧边栏的"学习计划"菜单</p>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>选择学习任务</h4>
                    <p>找到带有"有学习文件"标识的任务</p>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>启动学习模式</h4>
                    <p>点击"🎯 开始学习"按钮</p>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>深度学习</h4>
                    <p>查看文件内容和学习重点</p>
                </div>
                
                <div class="step">
                    <div class="step-number">5</div>
                    <h4>完成任务</h4>
                    <p>标记任务完成，更新学习进度</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 简单的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100 * (index + 1));
            });
            
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.style.opacity = '0';
                step.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    step.style.transition = 'all 0.6s ease';
                    step.style.opacity = '1';
                    step.style.transform = 'translateY(0)';
                }, 1000 + 100 * (index + 1));
            });
        });
    </script>
</body>
</html>
