// 任务学习器 - 处理点击式学习任务启动
class TaskLearner {
    constructor() {
        this.taskConfig = new TaskConfig();
        this.currentTask = null;
        this.currentFiles = [];
        this.currentFileIndex = 0;
    }

    // 启动学习任务
    async startTask(taskId) {
        const config = this.taskConfig.getTaskConfig(taskId);
        if (!config) {
            console.error('任务配置未找到:', taskId);
            return;
        }

        this.currentTask = { id: taskId, ...config };
        this.currentFiles = config.files || [];
        this.currentFileIndex = 0;

        // 切换到文档查看页面
        if (window.app) {
            window.app.showPage('document-viewer');
        }

        // 显示任务学习界面
        await this.showTaskLearningInterface();

        // 加载第一个文件
        if (this.currentFiles.length > 0) {
            await this.loadFile(0);
        }

        // 记录任务开始
        this.recordTaskStart(taskId);
    }

    // 显示任务学习界面
    async showTaskLearningInterface() {
        const container = document.getElementById('documentContent');
        if (!container) return;

        // 创建任务学习界面
        container.innerHTML = `
            <div class="task-learning-interface">
                <div class="task-header">
                    <div class="task-info">
                        <h2 class="task-title">${this.currentTask.title}</h2>
                        <p class="task-description">${this.currentTask.description}</p>
                    </div>
                    <div class="task-controls">
                        <button class="btn btn-secondary" id="showLearningGuide">
                            <i class="fas fa-book"></i> 学习指南
                        </button>
                        <button class="btn btn-primary" id="markTaskComplete">
                            <i class="fas fa-check"></i> 标记完成
                        </button>
                    </div>
                </div>
                
                <div class="task-content">
                    <div class="file-sidebar">
                        <h3>相关文件</h3>
                        <div class="file-list" id="taskFileList">
                            ${this.renderFileList()}
                        </div>
                    </div>
                    
                    <div class="file-viewer">
                        <div class="file-viewer-header">
                            <div class="file-info">
                                <span class="file-title" id="currentFileTitle">选择文件开始学习</span>
                                <span class="file-path" id="currentFilePath"></span>
                            </div>
                            <div class="file-controls">
                                <button class="btn btn-sm btn-secondary" id="prevFile" disabled>
                                    <i class="fas fa-chevron-left"></i> 上一个
                                </button>
                                <button class="btn btn-sm btn-secondary" id="nextFile">
                                    <i class="fas fa-chevron-right"></i> 下一个
                                </button>
                                <button class="btn btn-sm btn-secondary" id="copyFileContent">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                        </div>
                        
                        <div class="file-content" id="taskFileContent">
                            <div class="empty-state">
                                <i class="fas fa-file-code"></i>
                                <p>选择左侧文件开始学习</p>
                            </div>
                        </div>
                        
                        <div class="file-highlights" id="fileHighlights">
                            <!-- 学习重点将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定事件监听器
        this.bindTaskInterfaceEvents();
    }

    // 渲染文件列表
    renderFileList() {
        return this.currentFiles.map((file, index) => `
            <div class="file-item ${index === 0 ? 'active' : ''}" data-index="${index}">
                <div class="file-icon">
                    <i class="fas ${this.getFileIcon(file.type)}"></i>
                </div>
                <div class="file-details">
                    <div class="file-name">${file.title}</div>
                    <div class="file-description">${file.description}</div>
                    <div class="file-path-small">${file.path}</div>
                </div>
                <div class="file-status">
                    <i class="fas fa-circle-notch fa-spin loading-icon" style="display: none;"></i>
                    <i class="fas fa-check-circle success-icon" style="display: none;"></i>
                    <i class="fas fa-exclamation-circle error-icon" style="display: none;"></i>
                </div>
            </div>
        `).join('');
    }

    // 获取文件图标
    getFileIcon(fileType) {
        const iconMap = {
            'typescript': 'fa-file-code',
            'javascript': 'fa-file-code',
            'json': 'fa-file-code',
            'yaml': 'fa-file-alt',
            'markdown': 'fa-file-alt',
            'css': 'fa-file-code',
            'html': 'fa-file-code'
        };
        return iconMap[fileType] || 'fa-file';
    }

    // 绑定任务界面事件
    bindTaskInterfaceEvents() {
        // 文件列表点击事件
        document.querySelectorAll('.file-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.loadFile(index);
            });
        });

        // 导航按钮事件
        document.getElementById('prevFile')?.addEventListener('click', () => {
            this.navigateFile(-1);
        });

        document.getElementById('nextFile')?.addEventListener('click', () => {
            this.navigateFile(1);
        });

        // 复制文件内容
        document.getElementById('copyFileContent')?.addEventListener('click', () => {
            this.copyCurrentFileContent();
        });

        // 显示学习指南
        document.getElementById('showLearningGuide')?.addEventListener('click', () => {
            this.showLearningGuide();
        });

        // 标记任务完成
        document.getElementById('markTaskComplete')?.addEventListener('click', () => {
            this.markTaskComplete();
        });
    }

    // 加载文件
    async loadFile(index) {
        if (index < 0 || index >= this.currentFiles.length) return;

        const file = this.currentFiles[index];
        this.currentFileIndex = index;

        // 更新文件列表状态
        this.updateFileListState(index);

        // 显示加载状态
        this.showFileLoadingState(file);

        try {
            // 尝试加载文件内容
            let content = await this.taskConfig.getFileContent(file.path);
            
            // 如果无法加载，使用模拟内容
            if (!content) {
                content = this.getMockFileContent(file);
            }

            // 渲染文件内容
            this.renderFileContent(file, content);

            // 显示学习重点
            this.showFileHighlights(file);

            // 更新导航按钮状态
            this.updateNavigationButtons();

            // 标记文件加载成功
            this.markFileLoaded(index, true);

        } catch (error) {
            console.error('加载文件失败:', error);
            this.showFileError(file, error);
            this.markFileLoaded(index, false);
        }
    }

    // 更新文件列表状态
    updateFileListState(activeIndex) {
        document.querySelectorAll('.file-item').forEach((item, index) => {
            item.classList.toggle('active', index === activeIndex);
        });
    }

    // 显示文件加载状态
    showFileLoadingState(file) {
        document.getElementById('currentFileTitle').textContent = file.title;
        document.getElementById('currentFilePath').textContent = file.path;
        
        const content = document.getElementById('taskFileContent');
        content.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                <p>加载文件中...</p>
            </div>
        `;
    }

    // 渲染文件内容
    renderFileContent(file, content) {
        const container = document.getElementById('taskFileContent');
        
        if (file.type === 'markdown') {
            // Markdown 文件渲染
            container.innerHTML = marked.parse(content);
        } else {
            // 代码文件渲染
            const language = this.taskConfig.getLanguageByFileType(file.type);
            container.innerHTML = `
                <pre><code class="language-${language}">${this.escapeHtml(content)}</code></pre>
            `;
            
            // 应用语法高亮
            Prism.highlightAll();
        }

        // 添加代码复制按钮
        this.addCodeCopyButtons(container);
    }

    // 显示文件学习重点
    showFileHighlights(file) {
        const container = document.getElementById('fileHighlights');
        if (!file.highlights || file.highlights.length === 0) {
            container.innerHTML = '';
            return;
        }

        container.innerHTML = `
            <div class="highlights-section">
                <h4><i class="fas fa-lightbulb"></i> 学习重点</h4>
                <ul class="highlights-list">
                    ${file.highlights.map(highlight => `
                        <li class="highlight-item">
                            <i class="fas fa-arrow-right"></i>
                            <span>${highlight}</span>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    }

    // 文件导航
    navigateFile(direction) {
        const newIndex = this.currentFileIndex + direction;
        if (newIndex >= 0 && newIndex < this.currentFiles.length) {
            this.loadFile(newIndex);
        }
    }

    // 更新导航按钮状态
    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevFile');
        const nextBtn = document.getElementById('nextFile');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentFileIndex === 0;
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.currentFileIndex === this.currentFiles.length - 1;
        }
    }

    // 复制当前文件内容
    async copyCurrentFileContent() {
        const content = document.getElementById('taskFileContent');
        const textContent = content.textContent || content.innerText;
        
        try {
            await navigator.clipboard.writeText(textContent);
            
            // 显示复制成功提示
            const btn = document.getElementById('copyFileContent');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
            btn.style.color = 'var(--success-color)';
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.color = '';
            }, 2000);
            
        } catch (error) {
            console.error('复制失败:', error);
            alert('复制失败，请手动选择文本复制');
        }
    }

    // 显示学习指南
    showLearningGuide() {
        if (!this.currentTask.learningGuide) return;

        const modal = document.createElement('div');
        modal.className = 'modal active';
        modal.innerHTML = `
            <div class="modal-content learning-guide-modal">
                <div class="modal-header">
                    <h3>学习指南</h3>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="learning-guide-content">
                        ${marked.parse(this.currentTask.learningGuide)}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary close-guide">知道了</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定关闭事件
        modal.querySelector('.modal-close').addEventListener('click', () => {
            modal.remove();
        });
        
        modal.querySelector('.close-guide').addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    // 标记任务完成
    markTaskComplete() {
        if (!this.currentTask) return;

        // 更新任务状态
        if (window.app && window.app.dataManager) {
            // 这里需要根据任务ID找到对应的阶段任务并标记完成
            const taskId = this.currentTask.id;
            const [stageId, taskIndex] = this.parseTaskId(taskId);
            
            if (stageId && taskIndex !== null) {
                window.app.dataManager.updateStageTask(stageId, taskIndex, { completed: true });
                
                // 更新技能等级
                window.app.dataManager.updateSkillLevel(stageId);
                
                // 刷新相关页面
                if (window.app.progressTracker) {
                    window.app.progressTracker.updateLearningPlan();
                }
                window.app.updateDashboard();
            }
        }

        // 显示完成提示
        this.showTaskCompletionMessage();
    }

    // 解析任务ID
    parseTaskId(taskId) {
        const match = taskId.match(/^(stage\d+)_task(\d+)$/);
        if (match) {
            return [match[1], parseInt(match[2]) - 1]; // 转换为0基索引
        }
        return [null, null];
    }

    // 显示任务完成消息
    showTaskCompletionMessage() {
        const message = document.createElement('div');
        message.className = 'task-completion-message';
        message.innerHTML = `
            <div class="completion-content">
                <i class="fas fa-check-circle"></i>
                <h3>任务完成！</h3>
                <p>恭喜您完成了"${this.currentTask.title}"的学习</p>
                <button class="btn btn-primary" onclick="this.parentElement.parentElement.remove()">
                    继续学习
                </button>
            </div>
        `;
        
        message.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        `;
        
        message.querySelector('.completion-content').style.cssText = `
            background: var(--card-bg);
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            color: var(--text-color);
            max-width: 400px;
        `;

        document.body.appendChild(message);

        // 3秒后自动关闭
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    // 记录任务开始
    recordTaskStart(taskId) {
        const startTime = new Date().toISOString();
        localStorage.setItem(`task_start_${taskId}`, startTime);
    }

    // 标记文件加载状态
    markFileLoaded(index, success) {
        const fileItem = document.querySelector(`.file-item[data-index="${index}"]`);
        if (!fileItem) return;

        const loadingIcon = fileItem.querySelector('.loading-icon');
        const successIcon = fileItem.querySelector('.success-icon');
        const errorIcon = fileItem.querySelector('.error-icon');

        loadingIcon.style.display = 'none';
        
        if (success) {
            successIcon.style.display = 'inline';
            errorIcon.style.display = 'none';
        } else {
            successIcon.style.display = 'none';
            errorIcon.style.display = 'inline';
        }
    }

    // 显示文件错误
    showFileError(file, error) {
        const container = document.getElementById('taskFileContent');
        container.innerHTML = `
            <div class="file-error">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>无法加载文件</h3>
                <p>文件路径: ${file.path}</p>
                <p>错误信息: ${error.message}</p>
                <button class="btn btn-secondary" onclick="window.taskLearner.loadFile(${this.currentFileIndex})">
                    重试加载
                </button>
            </div>
        `;
    }

    // 获取模拟文件内容
    getMockFileContent(file) {
        return `// 模拟文件内容 - ${file.title}
// 文件路径: ${file.path}
// 
// 这是一个模拟的文件内容，用于演示学习功能。
// 在实际部署中，这里会显示真实的项目文件内容。
//
// 学习重点：
${file.highlights ? file.highlights.map(h => `// - ${h}`).join('\n') : '// 暂无学习重点'}

// 请参考学习指南了解更多详细信息。`;
    }

    // HTML 转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 添加代码复制按钮
    addCodeCopyButtons(container) {
        const codeBlocks = container.querySelectorAll('pre');
        codeBlocks.forEach(block => {
            if (block.querySelector('.code-copy-btn')) return; // 避免重复添加

            const copyButton = document.createElement('button');
            copyButton.className = 'code-copy-btn';
            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
            copyButton.title = '复制代码';
            
            copyButton.addEventListener('click', async () => {
                const code = block.querySelector('code').textContent;
                try {
                    await navigator.clipboard.writeText(code);
                    copyButton.innerHTML = '<i class="fas fa-check"></i>';
                    copyButton.style.color = 'var(--success-color)';
                    
                    setTimeout(() => {
                        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                        copyButton.style.color = '';
                    }, 2000);
                } catch (error) {
                    console.error('复制失败:', error);
                }
            });
            
            block.style.position = 'relative';
            block.appendChild(copyButton);
        });
    }
}

// 全局实例
window.taskLearner = new TaskLearner();
