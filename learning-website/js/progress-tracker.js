// 进度跟踪器
class ProgressTracker {
    constructor() {
        this.dataManager = null; // 将在 app.js 中设置
    }

    init(dataManager) {
        this.dataManager = dataManager;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 事件监听器已在 app.js 中设置
    }

    updateProgressTracking() {
        this.updateTimeRecordsTable();
        this.updateProgressChart();
        this.updateProgressStats();
    }

    updateTimeRecordsTable() {
        const records = this.dataManager.getTimeRecords();
        const tbody = document.getElementById('timeRecordsBody');

        if (!tbody) return;

        tbody.innerHTML = '';

        if (records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="4" class="empty-state">
                        <p>暂无学习记录</p>
                    </td>
                </tr>
            `;
            return;
        }

        // 按日期倒序排列
        const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));

        sortedRecords.forEach(record => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${this.formatDate(record.date)}</td>
                <td>${record.hours}h</td>
                <td>${record.content || '无描述'}</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="app.progressTracker.editRecord('${record.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.progressTracker.deleteRecord('${record.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateProgressChart() {
        const records = this.dataManager.getTimeRecords();
        const last30Days = this.getLast30DaysData(records);

        if (window.app && window.app.chartManager) {
            window.app.chartManager.updateProgressChart(last30Days);
        }
    }

    updateProgressStats() {
        const stats = this.dataManager.getStudyStats();

        // 更新统计卡片
        const elements = {
            todayTime: document.getElementById('todayTime'),
            weekTime: document.getElementById('weekTime'),
            totalTime: document.getElementById('totalTime'),
            streak: document.getElementById('streak')
        };

        Object.keys(elements).forEach(key => {
            if (elements[key]) {
                if (key.includes('Time')) {
                    elements[key].textContent = `${stats[key]}h`;
                } else {
                    elements[key].textContent = stats[key];
                }
            }
        });
    }

    getLast30DaysData(records) {
        const last30Days = [];

        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            const dayRecords = records.filter(record => record.date === dateStr);
            const totalHours = dayRecords.reduce((sum, record) => sum + record.hours, 0);

            last30Days.push({
                date: dateStr,
                hours: totalHours,
                label: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
            });
        }

        return last30Days;
    }

    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            weekday: 'short'
        });
    }

    editRecord(recordId) {
        const records = this.dataManager.getTimeRecords();
        const record = records.find(r => r.id === recordId);

        if (!record) return;

        // 填充表单
        document.getElementById('studyDate').value = record.date;
        document.getElementById('studyHours').value = record.hours;
        document.getElementById('studyContent').value = record.content || '';

        // 删除原记录（编辑时）
        this.dataManager.deleteTimeRecord(recordId);
        this.updateProgressTracking();
    }

    deleteRecord(recordId) {
        if (confirm('确定要删除这条学习记录吗？')) {
            this.dataManager.deleteTimeRecord(recordId);
            this.updateProgressTracking();

            // 更新仪表板
            if (window.app) {
                window.app.updateDashboard();
            }
        }
    }

    // 学习计划相关方法
    updateLearningPlan() {
        const stages = this.dataManager.getStageProgress();
        const container = document.getElementById('stagesContainer');

        if (!container) return;

        container.innerHTML = '';

        stages.forEach((stage, index) => {
            const stageElement = this.createStageElement(stage, index + 1);
            container.appendChild(stageElement);
        });
    }

    createStageElement(stage, stageNumber) {
        const completedTasks = stage.tasks.filter(task => task.completed).length;
        const totalTasks = stage.tasks.length;
        const progress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

        const stageElement = document.createElement('div');
        stageElement.className = 'stage-card';
        stageElement.innerHTML = `
            <div class="stage-header">
                <div class="stage-number">${stageNumber}</div>
                <div class="stage-meta">
                    <div class="stage-title">${stage.title}</div>
                    <div class="stage-duration">${stage.duration}</div>
                </div>
                <div class="stage-progress-indicator">
                    <div class="progress-percentage">${Math.round(progress)}%</div>
                    <div class="progress-label">完成度</div>
                </div>
            </div>
            <div class="stage-description">${stage.description}</div>
            <div class="stage-tasks">
                ${stage.tasks.map((task, taskIndex) => this.createTaskHTML(task, stage.id, taskIndex)).join('')}
            </div>
        `;

        // 添加任务点击事件
        const taskElements = stageElement.querySelectorAll('.task-status');
        taskElements.forEach((element, taskIndex) => {
            element.addEventListener('click', () => {
                this.toggleStageTask(stage.id, taskIndex);
            });
        });

        // 添加"开始学习"按钮点击事件
        const startLearningButtons = stageElement.querySelectorAll('.start-learning-btn');
        startLearningButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止触发任务状态切换
                const taskId = button.getAttribute('data-task-id');
                this.startTaskLearning(taskId);
            });
        });

        return stageElement;
    }

    createTaskHTML(task, stageId, taskIndex) {
        const taskId = `${stageId}_task${taskIndex + 1}`;
        const hasLearningFiles = window.taskLearner && window.taskLearner.taskConfig.getTaskConfig(taskId);

        return `
            <div class="stage-task-item ${task.completed ? 'completed' : ''}" data-task-id="${taskId}">
                <div class="task-status ${task.completed ? 'completed' : ''}" data-stage="${stageId}" data-task="${taskIndex}">
                    ${task.completed ? '<i class="fas fa-check"></i>' : ''}
                </div>
                <div class="task-content">
                    <div class="task-title">${task.title}</div>
                    <div class="task-meta">
                        <span><i class="fas fa-clock"></i> ${task.duration}h</span>
                        ${hasLearningFiles ? '<span class="task-files-indicator"><i class="fas fa-file-code"></i> 有学习文件</span>' : ''}
                    </div>
                </div>
                ${hasLearningFiles ? `
                    <div class="task-actions">
                        <button class="btn btn-sm btn-primary start-learning-btn" data-task-id="${taskId}">
                            <i class="fas fa-play"></i> 开始学习
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }

    toggleStageTask(stageId, taskIndex) {
        const stages = this.dataManager.getStageProgress();
        const stage = stages.find(s => s.id === stageId);

        if (stage && stage.tasks[taskIndex]) {
            stage.tasks[taskIndex].completed = !stage.tasks[taskIndex].completed;
            stage.tasks[taskIndex].completedAt = stage.tasks[taskIndex].completed ?
                new Date().toISOString() : null;

            this.dataManager.setStageProgress(stages);

            // 更新技能等级
            if (stage.tasks[taskIndex].completed) {
                this.dataManager.updateSkillLevel(stageId);
            }

            // 更新显示
            this.updateLearningPlan();

            // 更新仪表板
            if (window.app) {
                window.app.updateDashboard();
            }
        }
    }

    // 启动任务学习
    startTaskLearning(taskId) {
        if (window.taskLearner) {
            window.taskLearner.startTask(taskId);
        } else {
            console.error('TaskLearner 未初始化');
            alert('学习功能暂时不可用，请刷新页面重试。');
        }
    }

    // 实践项目相关方法
    updatePracticeProjects() {
        const projects = this.dataManager.getProjects();
        const container = document.getElementById('projectsGrid');

        if (!container) return;

        container.innerHTML = '';

        projects.forEach(project => {
            const projectElement = this.createProjectElement(project);
            container.appendChild(projectElement);
        });
    }

    createProjectElement(project) {
        const projectElement = document.createElement('div');
        projectElement.className = 'project-card';
        projectElement.innerHTML = `
            <div class="project-header">
                <div class="project-icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <div class="project-meta">
                    <div class="project-title">${project.title}</div>
                    <div class="project-difficulty">${project.difficulty}</div>
                </div>
                <div class="project-status ${project.status}">
                    ${this.getStatusText(project.status)}
                </div>
            </div>
            <div class="project-description">${project.description}</div>
            <div class="project-progress">
                <div class="project-progress-label">
                    <span>进度</span>
                    <span>${Math.round(project.progress)}%</span>
                </div>
                <div class="project-progress-bar">
                    <div class="project-progress-fill" style="width: ${project.progress}%"></div>
                </div>
            </div>
            <div class="project-actions">
                <button class="btn btn-primary" onclick="app.progressTracker.startProject('${project.id}')">
                    ${project.status === 'not-started' ? '开始项目' : '继续项目'}
                </button>
                <button class="btn btn-secondary" onclick="app.progressTracker.viewProject('${project.id}')">
                    查看详情
                </button>
            </div>
        `;

        return projectElement;
    }

    getStatusText(status) {
        const statusMap = {
            'not-started': '未开始',
            'in-progress': '进行中',
            'completed': '已完成'
        };
        return statusMap[status] || status;
    }

    startProject(projectId) {
        const projects = this.dataManager.getProjects();
        const project = projects.find(p => p.id === projectId);

        if (project && project.status === 'not-started') {
            project.status = 'in-progress';
            project.startedAt = new Date().toISOString();
            this.dataManager.setProjects(projects);
            this.updatePracticeProjects();
        }

        // 可以在这里添加更多项目启动逻辑
        alert(`项目 "${project.title}" 已开始！请查看实践项目文档了解详细要求。`);
    }

    viewProject(projectId) {
        const projects = this.dataManager.getProjects();
        const project = projects.find(p => p.id === projectId);

        if (project) {
            // 切换到文档查看页面并加载实践项目文档
            if (window.app) {
                window.app.showPage('document-viewer');
                document.getElementById('documentSelect').value = 'PRACTICE_PROJECTS.md';
                window.app.documentViewer.loadDocument('PRACTICE_PROJECTS.md');
            }
        }
    }

    // 设置页面相关方法
    updateSettings() {
        const settings = this.dataManager.getSettings();

        // 更新设置表单
        const dailyGoalInput = document.getElementById('dailyGoal');
        const enableRemindersInput = document.getElementById('enableReminders');
        const enableNotificationsInput = document.getElementById('enableNotifications');

        if (dailyGoalInput) dailyGoalInput.value = settings.dailyGoal || 2;
        if (enableRemindersInput) enableRemindersInput.checked = settings.enableReminders !== false;
        if (enableNotificationsInput) enableNotificationsInput.checked = settings.enableNotifications === true;
    }

    // 数据分析方法
    generateWeeklyReport() {
        const records = this.dataManager.getTimeRecords();
        const thisWeek = this.dataManager.getThisWeekDates();

        const weeklyData = thisWeek.map(date => {
            const dayRecords = records.filter(record => record.date === date);
            const totalHours = dayRecords.reduce((sum, record) => sum + record.hours, 0);
            return {
                date,
                hours: totalHours,
                day: new Date(date).toLocaleDateString('zh-CN', { weekday: 'short' })
            };
        });

        const totalWeekHours = weeklyData.reduce((sum, day) => sum + day.hours, 0);
        const avgDailyHours = totalWeekHours / 7;
        const studyDays = weeklyData.filter(day => day.hours > 0).length;

        return {
            weeklyData,
            totalWeekHours: totalWeekHours.toFixed(1),
            avgDailyHours: avgDailyHours.toFixed(1),
            studyDays,
            consistency: ((studyDays / 7) * 100).toFixed(0)
        };
    }

    generateMonthlyReport() {
        const records = this.dataManager.getTimeRecords();
        const thisMonth = this.getThisMonthDates();

        const monthlyData = thisMonth.map(date => {
            const dayRecords = records.filter(record => record.date === date);
            const totalHours = dayRecords.reduce((sum, record) => sum + record.hours, 0);
            return { date, hours: totalHours };
        });

        const totalMonthHours = monthlyData.reduce((sum, day) => sum + day.hours, 0);
        const studyDays = monthlyData.filter(day => day.hours > 0).length;
        const avgDailyHours = studyDays > 0 ? totalMonthHours / studyDays : 0;

        return {
            monthlyData,
            totalMonthHours: totalMonthHours.toFixed(1),
            avgDailyHours: avgDailyHours.toFixed(1),
            studyDays,
            consistency: ((studyDays / thisMonth.length) * 100).toFixed(0)
        };
    }

    getThisMonthDates() {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth();
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        const dates = [];
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            dates.push(date.toISOString().split('T')[0]);
        }

        return dates;
    }
}

// 扩展 app.js 中的方法
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        // 等待 app 初始化完成
        setTimeout(() => {
            if (window.app) {
                window.app.progressTracker = new ProgressTracker();
                window.app.progressTracker.init(window.app.dataManager);

                // 重写 app 中的方法
                window.app.updateLearningPlan = function() {
                    this.progressTracker.updateLearningPlan();
                };

                window.app.updateProgressTracking = function() {
                    this.progressTracker.updateProgressTracking();
                };

                window.app.updatePracticeProjects = function() {
                    this.progressTracker.updatePracticeProjects();
                };

                window.app.updateSettings = function() {
                    this.progressTracker.updateSettings();
                };
            }
        }, 100);
    });
}
