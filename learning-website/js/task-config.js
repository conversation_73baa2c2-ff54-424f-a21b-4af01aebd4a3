// 学习任务配置文件
class TaskConfig {
    constructor() {
        // 任务与文件的映射配置
        this.taskFileMapping = {
            // 第一阶段：项目结构和基础架构理解
            'stage1_task1': {
                title: '理解 package.json 配置',
                description: '深入分析项目的依赖管理和脚本配置',
                files: [
                    {
                        path: '../package.json',
                        type: 'json',
                        title: '根目录 package.json',
                        description: '项目主要依赖和脚本配置',
                        highlights: [
                            '查看 dependencies 和 devDependencies',
                            '理解 scripts 中的构建命令',
                            '分析 workspaces 配置'
                        ]
                    },
                    {
                        path: '../src/package.json',
                        type: 'json',
                        title: '扩展 package.json',
                        description: 'VS Code 扩展的配置文件',
                        highlights: [
                            '扩展的基本信息配置',
                            'contributes 字段的作用',
                            'activationEvents 激活事件'
                        ]
                    },
                    {
                        path: '../webview-ui/package.json',
                        type: 'json',
                        title: 'Webview UI package.json',
                        description: 'React 前端项目的依赖配置',
                        highlights: [
                            'React 相关依赖',
                            '构建工具配置',
                            '开发服务器设置'
                        ]
                    }
                ],
                learningGuide: `
# package.json 配置学习指南

## 学习目标
- 理解 Monorepo 项目中不同 package.json 的作用
- 掌握依赖管理的最佳实践
- 了解构建脚本的配置方法

## 重点分析
1. **根目录 package.json**：整个项目的依赖管理中心
2. **扩展 package.json**：VS Code 扩展的配置和元数据
3. **Webview package.json**：前端项目的独立依赖管理

## 实践练习
- 尝试添加新的依赖包
- 修改构建脚本
- 理解版本号的语义化规则
                `
            },

            'stage1_task2': {
                title: '掌握 Turbo 构建配置',
                description: '学习 Turbo 构建系统的配置和优化',
                files: [
                    {
                        path: '../turbo.json',
                        type: 'json',
                        title: 'Turbo 构建配置',
                        description: '构建任务依赖图和缓存配置',
                        highlights: [
                            'pipeline 任务依赖关系',
                            'cache 缓存策略配置',
                            'inputs 和 outputs 定义'
                        ]
                    },
                    {
                        path: '../pnpm-workspace.yaml',
                        type: 'yaml',
                        title: 'pnpm 工作区配置',
                        description: 'Monorepo 工作区的包管理配置',
                        highlights: [
                            'packages 路径配置',
                            '工作区依赖管理',
                            '包版本控制策略'
                        ]
                    }
                ],
                learningGuide: `
# Turbo 构建系统学习指南

## 学习目标
- 理解 Turbo 的构建优化原理
- 掌握任务依赖图的配置
- 学习缓存策略的设计

## 核心概念
1. **Pipeline**：定义任务之间的依赖关系
2. **Cache**：智能缓存提高构建效率
3. **Workspace**：多包项目的统一管理

## 实践要点
- 分析现有的构建流程
- 理解增量构建的优势
- 学习缓存失效的条件
                `
            },

            'stage1_task3': {
                title: '分析包依赖关系',
                description: '理解项目中各个包之间的依赖关系',
                files: [
                    {
                        path: '../packages/types/package.json',
                        type: 'json',
                        title: 'Types 包配置',
                        description: '共享类型定义包的配置',
                        highlights: [
                            '类型包的导出配置',
                            'TypeScript 编译选项',
                            '包的发布配置'
                        ]
                    },
                    {
                        path: '../packages/types/src/index.ts',
                        type: 'typescript',
                        title: 'Types 包入口文件',
                        description: '类型定义的统一导出',
                        highlights: [
                            '类型的重新导出',
                            '模块化组织方式',
                            'TypeScript 最佳实践'
                        ]
                    },
                    {
                        path: '../packages/types/src/global-settings.ts',
                        type: 'typescript',
                        title: '全局设置类型定义',
                        description: 'Zod schema 和 TypeScript 类型的结合',
                        highlights: [
                            'Zod schema 定义',
                            '类型推导的使用',
                            '运行时验证配置'
                        ]
                    }
                ],
                learningGuide: `
# 包依赖关系分析指南

## 学习目标
- 理解 Monorepo 中包的组织方式
- 掌握包之间的依赖管理
- 学习类型共享的最佳实践

## 分析重点
1. **Types 包**：提供全局类型定义
2. **依赖关系**：避免循环依赖
3. **版本管理**：保持版本一致性

## 实践练习
- 绘制包依赖关系图
- 分析类型定义的组织方式
- 理解 Zod + TypeScript 模式
                `
            },

            // 第二阶段：核心架构模式学习
            'stage2_task1': {
                title: '学习工厂模式实现',
                description: '分析 API Handler 中工厂模式的实际应用',
                files: [
                    {
                        path: '../src/api/index.ts',
                        type: 'typescript',
                        title: 'API 入口文件',
                        description: '工厂模式的核心实现',
                        highlights: [
                            'buildApiHandler 工厂函数',
                            '提供商类型的判断逻辑',
                            '配置参数的处理'
                        ]
                    },
                    {
                        path: '../src/api/providers/base-provider.ts',
                        type: 'typescript',
                        title: '基础提供商抽象类',
                        description: '定义所有 AI 提供商的通用接口',
                        highlights: [
                            '抽象方法的定义',
                            '通用功能的实现',
                            '接口设计原则'
                        ]
                    },
                    {
                        path: '../src/api/providers/anthropic.ts',
                        type: 'typescript',
                        title: 'Anthropic 提供商实现',
                        description: '具体提供商的实现示例',
                        highlights: [
                            '抽象方法的具体实现',
                            'API 调用的封装',
                            '错误处理机制'
                        ]
                    }
                ],
                learningGuide: `
# 工厂模式学习指南

## 学习目标
- 理解工厂模式的设计思想
- 掌握抽象类和接口的使用
- 学习多态性的实际应用

## 设计模式分析
1. **工厂方法**：根据配置创建不同的提供商实例
2. **抽象基类**：定义通用接口和共享逻辑
3. **具体实现**：各个 AI 提供商的特定逻辑

## 实践要点
- 分析工厂函数的实现逻辑
- 理解抽象类的设计原则
- 学习如何扩展新的提供商
                `
            },

            'stage2_task2': {
                title: '理解策略模式应用',
                description: '分析缓存系统中策略模式的实现',
                files: [
                    {
                        path: '../src/api/transform/cache-strategy/base-strategy.ts',
                        type: 'typescript',
                        title: '缓存策略基类',
                        description: '定义缓存策略的通用接口',
                        highlights: [
                            '策略接口的定义',
                            '缓存操作的抽象',
                            '策略切换的机制'
                        ]
                    },
                    {
                        path: '../src/api/transform/cache-strategy/multi-point-strategy.ts',
                        type: 'typescript',
                        title: '多点缓存策略',
                        description: '具体的缓存策略实现',
                        highlights: [
                            '多级缓存的实现',
                            '缓存失效策略',
                            '性能优化技巧'
                        ]
                    },
                    {
                        path: '../src/api/transform/cache-strategy/index.ts',
                        type: 'typescript',
                        title: '缓存策略管理器',
                        description: '策略的选择和管理逻辑',
                        highlights: [
                            '策略工厂的实现',
                            '动态策略切换',
                            '配置驱动的设计'
                        ]
                    }
                ],
                learningGuide: `
# 策略模式学习指南

## 学习目标
- 理解策略模式的应用场景
- 掌握算法族的封装方法
- 学习运行时策略切换

## 模式分析
1. **策略接口**：定义算法族的通用接口
2. **具体策略**：不同缓存算法的实现
3. **上下文类**：策略的使用和管理

## 实践重点
- 分析缓存策略的设计思路
- 理解策略切换的时机
- 学习性能优化的方法
                `
            },

            // 第三阶段：TypeScript 类型安全设计
            'stage3_task1': {
                title: '掌握 Zod schema 设计',
                description: '学习 Zod + TypeScript 的类型安全设计模式',
                files: [
                    {
                        path: '../packages/types/src/global-settings.ts',
                        type: 'typescript',
                        title: '全局设置 Schema',
                        description: 'Zod schema 的完整示例',
                        highlights: [
                            'schema 的定义方式',
                            '类型推导的使用',
                            '验证规则的配置'
                        ]
                    },
                    {
                        path: '../packages/types/src/api.ts',
                        type: 'typescript',
                        title: 'API 类型定义',
                        description: 'API 相关的类型和 schema',
                        highlights: [
                            '请求响应类型定义',
                            '泛型的使用',
                            '联合类型的应用'
                        ]
                    },
                    {
                        path: '../src/core/config/GlobalSettingsManager.ts',
                        type: 'typescript',
                        title: '设置管理器',
                        description: 'Zod schema 在实际项目中的使用',
                        highlights: [
                            'schema 验证的实现',
                            '类型安全的配置管理',
                            '错误处理机制'
                        ]
                    }
                ],
                learningGuide: `
# Zod + TypeScript 学习指南

## 学习目标
- 掌握 Zod schema 的定义方法
- 理解运行时类型验证的重要性
- 学习类型推导的最佳实践

## 核心概念
1. **Schema 定义**：使用 Zod 定义数据结构
2. **类型推导**：从 schema 自动推导 TypeScript 类型
3. **运行时验证**：确保数据的类型安全

## 实践要点
- 学习复杂 schema 的组合方式
- 理解验证错误的处理
- 掌握类型安全的配置管理
                `
            },

            // 第四阶段：前端组件和状态管理
            'stage4_task1': {
                title: '学习 React 组件设计',
                description: '分析 Webview 中 React 组件的设计模式',
                files: [
                    {
                        path: '../webview-ui/src/context/ExtensionStateContext.tsx',
                        type: 'typescript',
                        title: '扩展状态上下文',
                        description: '全局状态管理的实现',
                        highlights: [
                            'Context API 的使用',
                            'useReducer 状态管理',
                            '状态更新的优化'
                        ]
                    },
                    {
                        path: '../webview-ui/src/components/ChatInterface/ChatInterface.tsx',
                        type: 'typescript',
                        title: '聊天界面组件',
                        description: '复杂 React 组件的设计',
                        highlights: [
                            '组件状态管理',
                            '事件处理机制',
                            '性能优化技巧'
                        ]
                    },
                    {
                        path: '../src/core/webview/ClineProvider.ts',
                        type: 'typescript',
                        title: 'Webview 提供者',
                        description: 'VS Code Webview 的管理逻辑',
                        highlights: [
                            'Webview 生命周期管理',
                            '消息通信机制',
                            '资源管理策略'
                        ]
                    }
                ],
                learningGuide: `
# React 组件设计学习指南

## 学习目标
- 掌握 React Context 的状态管理模式
- 理解组件间通信的最佳实践
- 学习 VS Code Webview 的开发方法

## 设计模式
1. **Context 模式**：全局状态的管理和共享
2. **组件组合**：复杂 UI 的模块化设计
3. **通信机制**：Webview 与扩展的消息传递

## 实践重点
- 分析状态管理的设计思路
- 理解组件的职责分离
- 学习 Webview 通信协议
                `
            },

            // 第五阶段：算法和性能优化
            'stage5_task1': {
                title: '学习搜索算法优化',
                description: '分析文件搜索和模糊匹配算法的实现',
                files: [
                    {
                        path: '../src/services/search/file-search.ts',
                        type: 'typescript',
                        title: '文件搜索服务',
                        description: 'FZF 模糊搜索算法的实现',
                        highlights: [
                            '模糊匹配算法',
                            '搜索结果排序',
                            '性能优化策略'
                        ]
                    },
                    {
                        path: '../src/core/diff/strategies/multi-search-replace.ts',
                        type: 'typescript',
                        title: '多重搜索替换策略',
                        description: '字符串匹配和替换算法',
                        highlights: [
                            '字符串匹配算法',
                            '批量替换优化',
                            '正则表达式应用'
                        ]
                    },
                    {
                        path: '../src/core/tools/readFileTool.ts',
                        type: 'typescript',
                        title: '文件读取工具',
                        description: '文件操作的优化实现',
                        highlights: [
                            '异步文件操作',
                            '错误处理机制',
                            '内存使用优化'
                        ]
                    }
                ],
                learningGuide: `
# 算法和性能优化学习指南

## 学习目标
- 掌握搜索算法的优化技巧
- 理解字符串处理的高效方法
- 学习文件操作的性能优化

## 算法分析
1. **模糊搜索**：FZF 算法的实现原理
2. **字符串匹配**：高效的搜索替换策略
3. **文件操作**：异步处理和内存优化

## 优化重点
- 分析算法的时间复杂度
- 理解缓存策略的应用
- 学习并发处理的方法
                `
            }
        };

        // 文件类型与语言的映射
        this.fileTypeMapping = {
            'typescript': 'typescript',
            'javascript': 'javascript',
            'json': 'json',
            'yaml': 'yaml',
            'markdown': 'markdown',
            'css': 'css',
            'html': 'html'
        };
    }

    // 获取任务配置
    getTaskConfig(taskId) {
        return this.taskFileMapping[taskId] || null;
    }

    // 获取所有任务配置
    getAllTaskConfigs() {
        return this.taskFileMapping;
    }

    // 根据阶段获取任务
    getTasksByStage(stageId) {
        const tasks = {};
        Object.keys(this.taskFileMapping).forEach(taskId => {
            if (taskId.startsWith(stageId)) {
                tasks[taskId] = this.taskFileMapping[taskId];
            }
        });
        return tasks;
    }

    // 获取文件类型对应的语言
    getLanguageByFileType(fileType) {
        return this.fileTypeMapping[fileType] || 'text';
    }

    // 检查文件是否存在（模拟）
    async checkFileExists(filePath) {
        try {
            const response = await fetch(filePath, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // 获取文件内容
    async getFileContent(filePath) {
        try {
            const response = await fetch(filePath);
            if (response.ok) {
                return await response.text();
            }
        } catch (error) {
            console.warn('无法加载文件:', filePath, error);
        }
        return null;
    }
}
