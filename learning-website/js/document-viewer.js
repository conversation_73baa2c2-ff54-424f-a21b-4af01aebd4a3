// 文档查看器
class DocumentViewer {
    constructor() {
        this.currentDocument = null;
        this.documentCache = new Map();
        this.documentPaths = {
            'LEARNING_ROADMAP.md': '../LEARNING_ROADMAP.md',
            'QUICK_START_GUIDE.md': '../QUICK_START_GUIDE.md',
            'LEARNING_PROGRESS_TRACKER.md': '../LEARNING_PROGRESS_TRACKER.md',
            'PRACTICE_PROJECTS.md': '../PRACTICE_PROJECTS.md'
        };

        // 模拟文档内容（实际项目中应该从服务器加载）
        this.mockDocuments = {
            'LEARNING_ROADMAP.md': this.getLearningRoadmapContent(),
            'QUICK_START_GUIDE.md': this.getQuickStartContent(),
            'LEARNING_PROGRESS_TRACKER.md': this.getProgressTrackerContent(),
            'PRACTICE_PROJECTS.md': this.getPracticeProjectsContent()
        };
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 文档选择事件已在 app.js 中设置
    }

    async loadDocument(documentName) {
        try {
            this.showLoading();

            let content;

            // 首先尝试从缓存获取
            if (this.documentCache.has(documentName)) {
                content = this.documentCache.get(documentName);
            } else {
                // 尝试从文件系统加载（在实际部署中可能需要服务器支持）
                content = await this.fetchDocument(documentName);

                // 如果无法加载，使用模拟内容
                if (!content) {
                    content = this.mockDocuments[documentName] || '文档未找到';
                }

                // 缓存内容
                this.documentCache.set(documentName, content);
            }

            this.renderDocument(content);
            this.currentDocument = documentName;

        } catch (error) {
            console.error('加载文档失败:', error);
            this.showError('加载文档失败，请稍后重试。');
        }
    }

    async fetchDocument(documentName) {
        try {
            const path = this.documentPaths[documentName];
            if (!path) return null;

            const response = await fetch(path);
            if (response.ok) {
                return await response.text();
            }
        } catch (error) {
            console.warn('无法从文件系统加载文档:', error);
        }
        return null;
    }

    renderDocument(content) {
        const container = document.getElementById('documentContent');
        if (!container) return;

        try {
            // 使用 marked.js 渲染 Markdown
            const html = marked.parse(content);
            container.innerHTML = html;

            // 应用语法高亮
            this.applySyntaxHighlighting();

            // 添加目录导航
            this.addTableOfContents();

            // 添加代码复制功能
            this.addCodeCopyButtons();

            this.hideLoading();

        } catch (error) {
            console.error('渲染文档失败:', error);
            this.showError('渲染文档失败');
        }
    }

    applySyntaxHighlighting() {
        // 使用 Prism.js 进行语法高亮
        const codeBlocks = document.querySelectorAll('#documentContent pre code');
        codeBlocks.forEach(block => {
            // 自动检测语言
            const text = block.textContent;
            const language = this.detectLanguage(text);

            if (language) {
                block.className = `language-${language}`;
            }

            Prism.highlightElement(block);
        });
    }

    detectLanguage(code) {
        // 简单的语言检测
        if (code.includes('import') && code.includes('export')) return 'typescript';
        if (code.includes('function') && code.includes('=>')) return 'javascript';
        if (code.includes('npm install') || code.includes('pnpm')) return 'bash';
        if (code.includes('{') && code.includes('"')) return 'json';
        if (code.includes('<!DOCTYPE') || code.includes('<html')) return 'html';
        if (code.includes('display:') || code.includes('color:')) return 'css';
        return 'text';
    }

    addTableOfContents() {
        const container = document.getElementById('documentContent');
        const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');

        if (headings.length === 0) return;

        // 创建目录容器
        const tocContainer = document.createElement('div');
        tocContainer.className = 'table-of-contents';
        tocContainer.innerHTML = '<h3>目录</h3>';

        const tocList = document.createElement('ul');
        tocList.className = 'toc-list';

        headings.forEach((heading, index) => {
            // 为标题添加 ID
            const id = `heading-${index}`;
            heading.id = id;

            // 创建目录项
            const tocItem = document.createElement('li');
            tocItem.className = `toc-item toc-${heading.tagName.toLowerCase()}`;

            const tocLink = document.createElement('a');
            tocLink.href = `#${id}`;
            tocLink.textContent = heading.textContent;
            tocLink.addEventListener('click', (e) => {
                e.preventDefault();
                heading.scrollIntoView({ behavior: 'smooth' });
            });

            tocItem.appendChild(tocLink);
            tocList.appendChild(tocItem);
        });

        tocContainer.appendChild(tocList);

        // 插入到文档开头
        container.insertBefore(tocContainer, container.firstChild);
    }

    addCodeCopyButtons() {
        const codeBlocks = document.querySelectorAll('#documentContent pre');

        codeBlocks.forEach(block => {
            const copyButton = document.createElement('button');
            copyButton.className = 'code-copy-btn';
            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
            copyButton.title = '复制代码';

            copyButton.addEventListener('click', async () => {
                const code = block.querySelector('code').textContent;
                try {
                    await navigator.clipboard.writeText(code);
                    copyButton.innerHTML = '<i class="fas fa-check"></i>';
                    copyButton.style.color = 'var(--success-color)';

                    setTimeout(() => {
                        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                        copyButton.style.color = '';
                    }, 2000);
                } catch (error) {
                    console.error('复制失败:', error);
                }
            });

            block.style.position = 'relative';
            block.appendChild(copyButton);
        });
    }

    showLoading() {
        const container = document.getElementById('documentContent');
        if (container) {
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>加载文档中...</p>
                </div>
            `;
        }
    }

    hideLoading() {
        // Loading 状态会被新内容替换，无需特殊处理
    }

    showError(message) {
        const container = document.getElementById('documentContent');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${message}</p>
                </div>
            `;
        }
    }

    // 模拟文档内容
    getLearningRoadmapContent() {
        return `# Roo Code 项目学习路线图

> 基于 Roo Code 开源 AI 编程助手项目的系统性前端技术学习计划

## 📋 学习目标概览

### 核心技能提升目标
1. **架构设计能力** - Monorepo + Turbo 项目组织，设计模式实际应用
2. **TypeScript 进阶** - Zod + TypeScript 类型安全，泛型约束与类型推导
3. **现代前端开发** - React + TypeScript + Vite 技术栈深度应用
4. **AI 应用开发** - AI SDK 集成，提示工程，上下文管理
5. **VS Code 扩展开发** - 扩展 API，Webview 通信，工具集成

### 总学习周期：6-8 周（按每天 2-3 小时计算）

---

## 🎯 第一阶段：项目结构和基础架构理解

**⏱️ 时间安排：5-7 个工作日**

### 学习内容
- Monorepo 项目组织方式
- Turbo 构建系统配置
- 包依赖关系分析
- 开发工具链配置

### 🔍 重点文件分析

#### 必须深入研究的文件
\`\`\`
📁 根目录配置文件
├── package.json          ⭐⭐⭐ 主要依赖和脚本配置
├── turbo.json            ⭐⭐⭐ 构建任务依赖图
├── pnpm-workspace.yaml   ⭐⭐⭐ 工作区配置
└── tsconfig.json         ⭐⭐   TypeScript 基础配置
\`\`\`

### 🎯 学习重点

1. **Monorepo 架构理解**
   - 分析各子包的职责分工
   - 理解包之间的依赖关系
   - 掌握 pnpm workspace 的使用

2. **Turbo 构建系统**
   - 理解任务依赖图配置
   - 掌握增量构建原理
   - 学习缓存策略配置

### ✅ 验收标准

1. **理论验收**
   - 能够绘制出项目的包依赖关系图
   - 能够解释 Turbo 的构建优化原理
   - 理解各个配置文件的作用

2. **实践验收**
   - 能够成功克隆并启动项目
   - 能够添加新的子包并配置依赖
   - 能够修改 Turbo 配置并验证效果

---

## 🏗️ 第二阶段：核心架构模式学习

**⏱️ 时间安排：8-10 个工作日**

### 学习内容
- 工厂模式在 API Handler 中的应用
- 策略模式在缓存系统中的实现
- 继承层次设计的最佳实践
- 依赖注入和控制反转

### 🔍 重点文件分析

\`\`\`typescript
// 工厂模式实现示例
export function buildApiHandler(configuration: ProviderSettings): ApiHandler {
    switch (apiProvider) {
        case "anthropic": return new AnthropicHandler(options)
        case "openai": return new OpenAiHandler(options)
        // ...
    }
}
\`\`\`

---

## 🔧 第三阶段：TypeScript 类型安全设计

**⏱️ 时间安排：6-8 个工作日**

### 学习内容
- Zod schema 设计和类型推导
- 泛型约束和条件类型
- 类型安全的 API 设计
- 运行时类型验证

### 🎯 学习重点

\`\`\`typescript
// Zod + TypeScript 模式示例
export const globalSettingsSchema = z.object({
    apiConfiguration: providerSettingsSchema.optional(),
    customInstructions: z.string().optional(),
})
export type GlobalSettings = z.infer<typeof globalSettingsSchema>
\`\`\`

---

## ⚛️ 第四阶段：前端组件和状态管理

**⏱️ 时间安排：8-10 个工作日**

### 学习内容
- React 组件设计模式
- 复杂状态管理策略
- VS Code Webview 通信
- 性能优化技巧

---

## 🚀 第五阶段：算法和性能优化

**⏱️ 时间安排：6-8 个工作日**

### 学习内容
- 搜索和匹配算法实现
- 缓存策略和优化算法
- 并发控制和异步处理
- 性能监控和优化

---

## 📚 学习资源推荐

### 官方文档
- [VS Code Extension API](https://code.visualstudio.com/api)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)
- [Zod Documentation](https://zod.dev/)

---

## 🎯 总结

这个学习路线图基于 Roo Code 项目的实际架构和最佳实践设计，通过 5 个阶段的系统学习，您将掌握现代前端项目的完整技能栈。

**预期学习成果**：完成整个学习路线图后，您将具备独立开发复杂前端应用和 VS Code 扩展的能力。`;
    }

    getQuickStartContent() {
        return `# Roo Code 项目快速开始指南

> 基于学习路线图的快速上手指南，帮助您快速搭建开发环境并开始学习

## 🚀 环境准备

### 系统要求
- **Node.js**: >= 20.18.1
- **pnpm**: >= 10.8.1 (推荐使用 pnpm 而非 npm)
- **VS Code**: 最新版本
- **Git**: 用于版本控制

### 安装步骤

1. **安装 Node.js**
   \`\`\`bash
   # 使用 nvm 安装 (推荐)
   nvm install 20.18.1
   nvm use 20.18.1

   # 或直接从官网下载安装
   # https://nodejs.org/
   \`\`\`

2. **安装 pnpm**
   \`\`\`bash
   npm install -g pnpm@10.8.1

   # 验证安装
   pnpm --version
   \`\`\`

3. **克隆项目**
   \`\`\`bash
   git clone https://github.com/RooVetGit/Roo-Code.git
   cd Roo-Code
   \`\`\`

4. **安装依赖**
   \`\`\`bash
   # 安装所有依赖 (可能需要几分钟)
   pnpm install

   # 构建项目
   pnpm run build
   \`\`\`

## 📁 项目结构快速导览

\`\`\`
Roo-Code/
├── 📁 src/                    # 主扩展代码
│   ├── 📁 api/               # AI 提供商和 API 处理
│   ├── 📁 core/              # 核心功能模块
│   ├── 📁 services/          # 服务层代码
│   └── 📄 extension.ts       # 扩展入口文件
├── 📁 webview-ui/            # React 前端界面
├── 📁 packages/              # 共享包
└── 📄 turbo.json            # Turbo 构建配置
\`\`\`

## 🎯 第一周学习计划

### Day 1-2: 环境搭建和项目熟悉
- [ ] 完成环境搭建
- [ ] 成功启动项目
- [ ] 浏览项目目录结构

### Day 3-4: Monorepo 架构理解
- [ ] 分析各个子包的作用
- [ ] 理解包之间的依赖关系
- [ ] 学习 Turbo 构建系统

### Day 5-7: 核心架构模式
- [ ] 学习工厂模式实现
- [ ] 理解策略模式应用
- [ ] 分析继承层次设计

## 🛠️ 开发工具配置

### VS Code 扩展推荐
\`\`\`json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint"
  ]
}
\`\`\`

### 有用的 VS Code 快捷键
- \`F5\` - 启动扩展调试
- \`Ctrl+Shift+P\` - 命令面板
- \`Ctrl+Shift+I\` - 开发者工具

## 🧪 测试和验证

### 运行测试
\`\`\`bash
# 运行所有测试
pnpm test

# 运行特定测试文件
pnpm test -- src/core/tools/__tests__/readFileTool.test.ts
\`\`\`

### 类型检查
\`\`\`bash
# 检查 TypeScript 类型错误
pnpm run type-check
\`\`\`

## 🔧 常见问题解决

### 安装问题

**问题**: \`pnpm install\` 失败
\`\`\`bash
# 解决方案1: 清理缓存
pnpm store prune
rm -rf node_modules
pnpm install
\`\`\`

**问题**: Node.js 版本不兼容
\`\`\`bash
# 使用 nvm 切换版本
nvm install 20.18.1
nvm use 20.18.1
\`\`\`

## 🎯 下一步行动

1. **完成环境搭建** - 确保项目能够正常运行
2. **开始第一阶段学习** - 按照学习路线图进行
3. **建立学习习惯** - 每天固定时间学习 2-3 小时

**记住**：学习是一个渐进的过程，不要急于求成。遇到问题时，先查阅文档，再寻求帮助。

祝您学习顺利！🚀`;
    }

    getProgressTrackerContent() {
        return `# Roo Code 学习进度跟踪表

> 用于跟踪学习进度、记录完成情况和反思总结的工具表格

## 📊 总体进度概览

| 学习阶段 | 预计时间 | 开始日期 | 完成日期 | 进度状态 | 完成度 |
|---------|---------|---------|---------|---------|--------|
| 第一阶段：项目结构和基础架构 | 5-7天 | ___/___/___ | ___/___/___ | ⏳ 未开始 | 0% |
| 第二阶段：核心架构模式学习 | 8-10天 | ___/___/___ | ___/___/___ | ⏳ 未开始 | 0% |
| 第三阶段：TypeScript 类型安全 | 6-8天 | ___/___/___ | ___/___/___ | ⏳ 未开始 | 0% |
| 第四阶段：前端组件和状态管理 | 8-10天 | ___/___/___ | ___/___/___ | ⏳ 未开始 | 0% |
| 第五阶段：算法和性能优化 | 6-8天 | ___/___/___ | ___/___/___ | ⏳ 未开始 | 0% |

**状态说明**：
- ⏳ 未开始
- 🟡 进行中
- ✅ 已完成
- ❌ 需要重新学习

---

## 🎯 第一阶段：项目结构和基础架构理解

### 学习目标完成情况

| 学习内容 | 重要程度 | 完成状态 | 完成日期 | 学习笔记 |
|---------|---------|---------|---------|---------|
| Monorepo 项目组织方式 | ⭐⭐⭐ | ⬜ | ___/___/___ | [链接] |
| Turbo 构建系统配置 | ⭐⭐⭐ | ⬜ | ___/___/___ | [链接] |
| 包依赖关系分析 | ⭐⭐⭐ | ⬜ | ___/___/___ | [链接] |
| 开发工具链配置 | ⭐⭐ | ⬜ | ___/___/___ | [链接] |

### 重点文件学习进度

| 文件路径 | 学习深度 | 完成状态 | 理解程度 | 备注 |
|---------|---------|---------|---------|------|
| \`package.json\` | 深入研究 | ⬜ | ___% | |
| \`turbo.json\` | 深入研究 | ⬜ | ___% | |
| \`pnpm-workspace.yaml\` | 深入研究 | ⬜ | ___% | |

### 实践练习完成情况

| 练习项目 | 难度 | 完成状态 | 完成日期 | 遇到的问题 | 解决方案 |
|---------|------|---------|---------|-----------|---------|
| 创建新的配置包 | 🟢 简单 | ⬜ | ___/___/___ | | |
| 分析构建依赖 | 🟡 中等 | ⬜ | ___/___/___ | | |

---

## 📈 学习统计

### 时间投入统计

| 日期 | 学习时间 | 学习内容 | 完成的任务 | 遇到的问题 |
|------|---------|---------|-----------|-----------|
| ___/___/___ | ___小时 | | | |
| ___/___/___ | ___小时 | | | |

**总学习时间**: _____ 小时

### 技能提升评估

| 技能领域 | 学习前水平 | 当前水平 | 目标水平 | 提升幅度 |
|---------|-----------|---------|---------|---------|
| Monorepo 架构 | ___/10 | ___/10 | 8/10 | +___ |
| TypeScript 高级特性 | ___/10 | ___/10 | 8/10 | +___ |
| React 状态管理 | ___/10 | ___/10 | 8/10 | +___ |

---

## 🎯 下周学习计划

### 本周总结
**完成的主要任务**：
- [ ]
- [ ]

**遇到的主要困难**：
- [ ]
- [ ]

### 下周学习目标
**优先级任务**：
1.
2.
3.

**预计学习时间**: _____ 小时

---

## 📝 学习反思

### 学习方法评估
**有效的学习方法**：
- [ ]
- [ ]

**需要改进的地方**：
- [ ]
- [ ]

---

**使用说明**：
1. 定期更新进度状态和完成日期
2. 记录学习过程中的问题和解决方案
3. 每周进行学习反思和计划调整
4. 保持学习笔记的链接更新
5. 诚实评估自己的理解程度和技能水平`;
    }

    getPracticeProjectsContent() {
        return `# Roo Code 学习实践项目集

> 基于学习路线图设计的渐进式实践项目，帮助巩固每个阶段的学习成果

## 🎯 项目概览

| 项目名称 | 对应阶段 | 难度等级 | 预计时间 | 核心技能 |
|---------|---------|---------|---------|---------|
| Mini Monorepo | 第1-2阶段 | 🟢 初级 | 3-5天 | Monorepo、设计模式 |
| Type-Safe Config | 第3阶段 | 🟡 中级 | 2-3天 | TypeScript、Zod |
| React State Manager | 第4阶段 | 🟡 中级 | 4-6天 | React、状态管理 |
| VS Code Extension | 第4阶段 | 🔴 高级 | 5-7天 | 扩展开发、Webview |
| Performance Optimizer | 第5阶段 | 🔴 高级 | 4-6天 | 算法、性能优化 |

---

## 🚀 项目1：Mini Monorepo - 简化版 AI 助手

**适用阶段**: 第1-2阶段完成后
**难度等级**: 🟢 初级
**预计时间**: 3-5天

### 项目目标
创建一个基础的 AI 助手框架，重点学习 Monorepo 架构和设计模式的实际应用。

### 技能要求
- [x] 完成第1阶段：项目结构和基础架构理解
- [x] 完成第2阶段：核心架构模式学习
- [ ] 基础的 Node.js 和 TypeScript 知识

### 项目结构
\`\`\`
mini-ai-assistant/
├── 📁 packages/
│   ├── 📁 core/                 # 核心逻辑包
│   ├── 📁 providers/            # AI 提供商包
│   └── 📁 types/                # 共享类型包
├── 📁 apps/
│   └── 📁 cli/                  # 命令行应用
├── 📄 package.json              # 根包配置
├── 📄 pnpm-workspace.yaml       # 工作区配置
└── 📄 turbo.json               # 构建配置
\`\`\`

### 核心功能实现

#### 1. 工厂模式实现
\`\`\`typescript
import { BaseProvider } from './base-provider'
import { MockProvider } from './mock-provider'
import { OpenAIProvider } from './openai-provider'

export function createProvider(config: ProviderConfig): BaseProvider {
    switch (config.type) {
        case ProviderType.MOCK:
            return new MockProvider(config)
        case ProviderType.OPENAI:
            return new OpenAIProvider(config)
        default:
            throw new Error(\`Unsupported provider type: \${config.type}\`)
    }
}
\`\`\`

#### 2. 抽象基类设计
\`\`\`typescript
export abstract class BaseProvider {
    protected config: ProviderConfig

    constructor(config: ProviderConfig) {
        this.config = config
    }

    abstract async sendMessage(message: Message): Promise<Response>
    abstract async validateConfig(): Promise<boolean>

    protected formatMessage(message: Message): string {
        return \`\${message.role}: \${message.content}\`
    }
}
\`\`\`

### 学习重点

1. **Monorepo 架构实践**
   - 理解包之间的依赖关系
   - 掌握 pnpm workspace 的使用
   - 学习 Turbo 构建优化

2. **设计模式应用**
   - 工厂模式的实际实现
   - 抽象基类的设计原则
   - 策略模式的变体应用

### 验收标准

#### 功能验收
- [ ] 能够成功创建不同类型的 AI 提供商
- [ ] 命令行应用能够正常工作
- [ ] 所有包都能正确构建和测试
- [ ] 类型检查无错误

#### 代码质量验收
- [ ] 遵循 SOLID 原则
- [ ] 具有良好的错误处理
- [ ] 包含必要的单元测试
- [ ] 代码注释清晰完整

---

## 🔧 项目2：Type-Safe Config - 类型安全的配置系统

**适用阶段**: 第3阶段完成后
**难度等级**: 🟡 中级
**预计时间**: 2-3天

### 项目目标
实现一个完整的配置管理系统，重点学习 Zod + TypeScript 的类型安全设计。

### 核心功能实现

#### 1. Zod Schema 设计
\`\`\`typescript
import { z } from 'zod'

// 基础配置 schema
export const baseConfigSchema = z.object({
    name: z.string().min(1, "配置名称不能为空"),
    version: z.string().regex(/^\d+\.\d+\.\d+$/, "版本格式必须为 x.y.z"),
    description: z.string().optional(),
    enabled: z.boolean().default(true),
})

// 完整应用配置 schema
export const appConfigSchema = z.object({
    ...baseConfigSchema.shape,
    database: databaseConfigSchema,
    api: apiConfigSchema,
    features: z.record(z.string(), z.boolean()).default({}),
})

// 类型推导
export type AppConfig = z.infer<typeof appConfigSchema>
\`\`\`

#### 2. 泛型配置管理器
\`\`\`typescript
export class ConfigManager<T extends z.ZodType> {
    private schema: T
    private config: z.infer<T> | null = null

    constructor(schema: T, configPath: string) {
        this.schema = schema
        this.configPath = path.resolve(configPath)
    }

    async load(): Promise<z.infer<T>> {
        const configData = await fs.readFile(this.configPath, 'utf-8')
        const rawConfig = JSON.parse(configData)

        // 使用 Zod 验证和转换
        const validatedConfig = this.schema.parse(rawConfig)
        this.config = validatedConfig

        return validatedConfig
    }
}
\`\`\`

### 学习重点

1. **Zod Schema 设计**
   - 复杂对象的 schema 定义
   - 自定义验证规则
   - 默认值和可选字段处理

2. **泛型约束应用**
   - 泛型类的设计
   - 类型推导和类型安全
   - 条件类型的使用

---

## ⚛️ 项目3：React State Manager - 复杂状态管理系统

**适用阶段**: 第4阶段完成后
**难度等级**: 🟡 中级
**预计时间**: 4-6天

### 项目目标
实现一个类似 Redux 但更简单的状态管理库，学习 React Context、Reducer 模式和性能优化。

### 核心功能实现

#### 1. 核心 Store 实现
\`\`\`typescript
export class SimpleStore<S> implements Store<S> {
    private state: S
    private reducer: Reducer<S, Action>
    private listeners: Set<() => void> = new Set()

    constructor(config: StoreConfig<S>) {
        this.state = config.initialState
        this.reducer = config.reducer
    }

    getState(): S {
        return this.state
    }

    dispatch(action: Action): void {
        this.state = this.reducer(this.state, action)
        this.notifyListeners()
    }

    subscribe(listener: () => void): () => void {
        this.listeners.add(listener)
        return () => this.listeners.delete(listener)
    }
}
\`\`\`

#### 2. React Context 封装
\`\`\`typescript
export function createStoreContext<S>() {
    const StoreContext = createContext<StoreContextValue<S> | null>(null)

    function StoreProvider({ config, children }: StoreProviderProps) {
        const [store] = React.useState(() => createStore(config))

        return (
            <StoreContext.Provider value={{ store }}>
                {children}
            </StoreContext.Provider>
        )
    }

    function useStore(): Store<S> {
        const context = useContext(StoreContext)
        if (!context) {
            throw new Error('useStore must be used within a StoreProvider')
        }
        return context.store
    }

    return { StoreProvider, useStore }
}
\`\`\`

---

## 🖥️ 项目4：VS Code Extension - 代码助手扩展

**适用阶段**: 第4阶段完成后
**难度等级**: 🔴 高级
**预计时间**: 5-7天

### 项目目标
开发一个完整的 VS Code 扩展，集成 Webview、命令、设置等功能。

### 核心功能实现

#### 1. 扩展入口
\`\`\`typescript
export function activate(context: vscode.ExtensionContext) {
    // 创建 Webview 管理器
    const webviewManager = new WebviewManager(context)

    // 注册侧边栏 Provider
    const provider = new CodeAssistantProvider(context, webviewManager)
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            CodeAssistantProvider.viewType,
            provider
        )
    )

    // 注册命令
    registerCommands(context, webviewManager)
}
\`\`\`

#### 2. Webview 通信
\`\`\`typescript
export class WebviewManager {
    private webview: vscode.Webview | undefined

    public setupWebview(webview: vscode.Webview) {
        this.webview = webview

        // 监听来自 Webview 的消息
        webview.onDidReceiveMessage(async (message: WebviewMessage) => {
            await this.handleWebviewMessage(message)
        })
    }

    private async handleWebviewMessage(message: WebviewMessage) {
        switch (message.type) {
            case 'getActiveDocument':
                await this.handleGetActiveDocument()
                break
            case 'insertCode':
                await this.handleInsertCode(message.payload)
                break
        }
    }
}
\`\`\`

---

## 🚀 项目5：Performance Optimizer - 性能优化工具

**适用阶段**: 第5阶段完成后
**难度等级**: 🔴 高级
**预计时间**: 4-6天

### 项目目标
创建一个代码性能分析和优化工具，重点学习算法优化、并发处理和性能监控技术。

### 核心功能实现

#### 1. 复杂度分析器
\`\`\`typescript
export class ComplexityAnalyzer {
    public analyze(): ComplexityMetrics {
        const visitor = new ComplexityVisitor()
        visitor.visit(this.sourceFile)

        return {
            cyclomaticComplexity: visitor.getCyclomaticComplexity(),
            cognitiveComplexity: visitor.getCognitiveComplexity(),
            nestingDepth: visitor.getMaxNestingDepth(),
            linesOfCode: visitor.getLinesOfCode(),
            maintainabilityIndex: this.calculateMaintainabilityIndex(visitor)
        }
    }
}
\`\`\`

#### 2. 优化搜索算法
\`\`\`typescript
export class OptimizedSearch<T> {
    // 模糊搜索算法 - 使用 Levenshtein 距离
    public fuzzySearch(query: string, maxResults: number = 10): SearchResult<T>[] {
        const queryWords = this.tokenize(query)
        const candidates = new Map<T, number>()

        // 使用索引快速找到候选项
        queryWords.forEach(queryWord => {
            this.indexedItems.forEach((items, indexWord) => {
                const distance = this.levenshteinDistance(queryWord, indexWord)
                const maxDistance = Math.floor(queryWord.length * 0.3)

                if (distance <= maxDistance) {
                    const score = 1 - (distance / queryWord.length)
                    items.forEach(item => {
                        const currentScore = candidates.get(item) || 0
                        candidates.set(item, currentScore + score)
                    })
                }
            })
        })

        return Array.from(candidates.entries())
            .map(([item, score]) => ({ item, score, index: this.items.indexOf(item) }))
            .sort((a, b) => b.score - a.score)
            .slice(0, maxResults)
    }
}
\`\`\`

---

## 📈 学习进度建议

### 项目完成顺序
1. **项目1 → 项目2** - 建立基础架构和类型安全理解
2. **项目3 → 项目4** - 掌握前端开发和扩展开发技能
3. **项目5** - 综合应用所有学到的技术

### 时间分配建议
- **总时间**: 20-30天（每天2-3小时）
- **项目1**: 3-5天（基础架构）
- **项目2**: 2-3天（类型安全）
- **项目3**: 4-6天（状态管理）
- **项目4**: 5-7天（扩展开发）
- **项目5**: 4-6天（性能优化）

---

## 🎯 学习成果验证

### 技能评估标准

#### 初级水平 (项目1-2完成后)
- [ ] 能够理解和搭建 Monorepo 项目
- [ ] 掌握基本的设计模式应用
- [ ] 能够使用 Zod 进行类型验证
- [ ] 理解 TypeScript 高级特性

#### 中级水平 (项目3-4完成后)
- [ ] 能够设计复杂的状态管理系统
- [ ] 掌握 React 性能优化技巧
- [ ] 能够开发完整的 VS Code 扩展
- [ ] 理解 Webview 通信机制

#### 高级水平 (项目5完成后)
- [ ] 能够分析和优化算法性能
- [ ] 掌握并发处理和缓存策略
- [ ] 能够设计性能监控系统
- [ ] 具备系统架构设计能力

---

## 🚀 进阶学习建议

### 完成所有项目后的下一步

1. **开源贡献**
   - 向 Roo Code 项目贡献代码
   - 参与其他开源项目
   - 维护自己的开源项目

2. **技术深化**
   - 学习更多 AI 相关技术
   - 深入研究编译器和语言服务器
   - 探索 WebAssembly 和性能优化

3. **架构设计**
   - 学习微服务架构
   - 掌握分布式系统设计
   - 研究大规模系统架构

---

**最后更新**: 2024年12月
**文档版本**: v1.0
**适用于**: Roo Code 项目学习者`;
    }
}
