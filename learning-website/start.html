<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动 Roo Code 学习管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .start-button {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .start-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .features {
            margin-top: 3rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .feature h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .instructions {
            margin-top: 2rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: left;
        }
        
        .instructions h3 {
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .instructions ol {
            padding-left: 1.5rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }
        
        .instructions code {
            background: rgba(0, 0, 0, 0.3);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1>Roo Code 学习管理系统</h1>
        <p class="subtitle">基于 Roo Code 项目的系统性前端技术学习平台</p>
        
        <a href="index.html" class="start-button">
            🎯 开始学习之旅
        </a>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>进度管理</h3>
                <p>5个学习阶段，详细任务清单，可视化进度跟踪</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📈</div>
                <h3>数据分析</h3>
                <p>学习时间统计，效率分析，技能雷达图展示</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📚</div>
                <h3>文档集成</h3>
                <p>内置文档浏览器，Markdown渲染，代码高亮</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💾</div>
                <h3>数据持久化</h3>
                <p>本地存储，数据导入导出，跨设备同步</p>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🛠️ 本地运行说明</h3>
            <ol>
                <li><strong>VS Code Live Server（推荐）</strong>
                    <br>安装 Live Server 扩展，右键 <code>index.html</code> 选择 "Open with Live Server"
                </li>
                <li><strong>Python 服务器</strong>
                    <br><code>python -m http.server 8000</code>
                </li>
                <li><strong>Node.js 服务器</strong>
                    <br><code>npx serve .</code>
                </li>
                <li><strong>PHP 服务器</strong>
                    <br><code>php -S localhost:8000</code>
                </li>
            </ol>
            <p style="margin-top: 1rem; text-align: center; opacity: 0.8;">
                💡 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）获得最佳体验
            </p>
        </div>
    </div>
    
    <script>
        // 简单的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    feature.style.transition = 'all 0.6s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, 200 * (index + 1));
            });
            
            // 检查是否支持必要的功能
            if (!window.localStorage) {
                alert('您的浏览器不支持 localStorage，部分功能可能无法正常使用。建议使用现代浏览器。');
            }
            
            // 检查是否是通过文件协议访问
            if (location.protocol === 'file:') {
                const warning = document.createElement('div');
                warning.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(255, 193, 7, 0.9);
                    color: #000;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-size: 14px;
                    z-index: 1000;
                `;
                warning.textContent = '⚠️ 建议通过本地服务器访问以获得完整功能';
                document.body.appendChild(warning);
                
                setTimeout(() => {
                    warning.remove();
                }, 5000);
            }
        });
    </script>
</body>
</html>
